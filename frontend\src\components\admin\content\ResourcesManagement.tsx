import React, { useState, useEffect } from 'react';
import { Search, Edit, Trash2, Plus, BookOpen, ExternalLink, X, Check, RefreshCw } from 'lucide-react';
import { Resource, resourcesAPI } from '../../../services/api';
import { useAppSelector } from '../../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
interface ResourceFormData {
  title: string;
  description: string;
  resource_type: string;
  url: string;
}

const ResourcesManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { user } = useAppSelector(state => state.auth);
  const [resources, setResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedResource, setSelectedResource] = useState<Resource | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [filter, setFilter] = useState<string>('all');
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [formSubmitting, setFormSubmitting] = useState(false);

  // Form data for create/edit
  const [formData, setFormData] = useState<ResourceFormData>({
    title: '',
    description: '',
    resource_type: 'article',
    url: ''
  });

  // Function to fetch resources directly using fetch API
  const fetchResources = async (retryCount = 0) => {
    setLoading(true);
    setFormError(null);

    try {
      // Use direct fetch instead of the API service
      console.log('Directly fetching resources... (attempt ' + (retryCount + 1) + ')');

      const response = await fetch('http://localhost:8000/api/resources/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = await response.json();
      console.log("Resources fetched directly:", data);

      // Check if response has the expected paginated structure
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        console.log("Successfully extracted resources from paginated response:", data.results);
        setResources(data.results);
        setLoading(false);
      } else if (Array.isArray(data)) {
        // If response is already an array (non-paginated)
        setResources(data);
        setLoading(false);
      } else {
        console.error("Received unexpected response format:", data);

        if (retryCount < 1) {
          console.log("Retrying direct fetch...");
          setLoading(false); // Set loading to false before retrying
          setTimeout(() => fetchResources(retryCount + 1), 1000);
        } else {
          setFormError(t("admin.received.invalid.data", "Received invalid data format. Please try again."));
          setLoading(false);
        }
      }
    } catch (error) {
      console.error('Error fetching resources:', error);

      if (retryCount < 1) {
        console.log('Retrying after error...');
        setLoading(false); // Set loading to false before retrying
        setTimeout(() => fetchResources(retryCount + 1), 1000);
      } else {
        setFormError(t("admin.failed.to.load", "Failed to load resources. Please try again."));
        setLoading(false);
      }
    }
  };

  // Fetch resources on component mount with a slight delay
  useEffect(() => {
    // Make sure user is authenticated before fetching resources
    if (user?.id) {
      console.log("User authenticated, fetching resources...");

      // Add a small delay before fetching resources
      // This helps ensure the component is fully mounted and authenticated
      const fetchTimer = setTimeout(() => {
        fetchResources(0); // Start with a fresh fetch attempt
      }, 300);

      // Add a timeout to stop loading after 10 seconds if it's still loading
      const loadingTimeoutTimer = setTimeout(() => {
        setLoading(prevLoading => {
          if (prevLoading) {
            console.log("Loading timeout reached, stopping loading state");
            setFormError(t("admin.loading.took.too", "Loading took too long. Please try again or use sample data."));
            return false;
          }
          return prevLoading;
        });
      }, 10000);

      // Clean up the timers if the component unmounts
      return () => {
        clearTimeout(fetchTimer);
        clearTimeout(loadingTimeoutTimer);
      };
    } else {
      console.log("Waiting for user authentication...");
    }
  }, [user?.id]); // Re-run when user ID changes (i.e., when user becomes authenticated)

  // Filter resources based on search term and type filter
  const filteredResources = resources.filter(resource => {
    const matchesSearch =
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filter === 'all' || resource.resource_type === filter;

    return matchesSearch && matchesFilter;
  });

  // Get resource type label
  const getResourceTypeLabel = (type: string) => {
    switch (type) {
      case 'article':
        return t('resources.types.article');
      case 'video':
        return t('resources.types.video');
      case 'course':
        return t('resources.types.course');
      case 'book':
        return t('resources.types.book');
      case 'tool':
        return t('resources.types.tool');
      default:
        return t('resources.types.other');
    }
  };

  // Get resource type color
  const getResourceTypeColor = (type: string) => {
    switch (type) {
      case 'article':
        return 'bg-blue-600/30 text-blue-400';
      case 'video':
        return 'bg-red-600/30 text-red-400';
      case 'course':
        return 'bg-green-600/30 text-green-400';
      case 'book':
        return 'bg-yellow-600/30 text-yellow-400';
      case 'tool':
        return 'bg-purple-600/30 text-purple-400';
      default:
        return 'bg-gray-600/30 text-gray-400';
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      resource_type: 'article',
      url: ''
    });
    setFormError(null);
    setFormSuccess(null);
  };

  // Open create modal
  const openCreateModal = () => {
    resetForm();
    setIsCreateModalOpen(true);
  };

  // Handle edit resource
  const handleEditResource = (resource: Resource) => {
    setSelectedResource(resource);
    setFormData({
      title: resource.title,
      description: resource.description,
      resource_type: resource.resource_type,
      url: resource.url
    });
    setIsEditModalOpen(true);
  };

  // Handle delete resource
  const handleDeleteResource = (resource: Resource) => {
    setSelectedResource(resource);
    setIsDeleteModalOpen(true);
  };

  // Create new resource
  const createResource = async () => {
    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.description || !formData.url) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      // Create resource data
      const resourceData = {
        title: formData.title,
        description: formData.description,
        resource_type: formData.resource_type,
        url: formData.url,
        author_id: user?.id || 0
      };

      // Call API to create resource
      const newResource = await resourcesAPI.createResource(resourceData);

      // Update local state
      setResources([newResource, ...resources]);

      // Show success message
      setFormSuccess(t("admin.resource.created.successfully", "Resource created successfully!"));

      // Close modal after a delay
      setTimeout(() => {
        setIsCreateModalOpen(false);
        resetForm();
      }, 1500);
    } catch (error) {
      console.error('Error creating resource:', error);
      setFormError(t("admin.failed.to.create", "Failed to create resource. Please try again."));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Update resource
  const updateResource = async () => {
    if (!selectedResource) return;

    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.description || !formData.url) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      // Create resource data
      const resourceData = {
        title: formData.title,
        description: formData.description,
        resource_type: formData.resource_type,
        url: formData.url
      };

      // Call API to update resource
      const updatedResource = await resourcesAPI.updateResource(selectedResource.id, resourceData);

      // Update local state
      setResources(resources.map(resource => resource.id === updatedResource.id ? updatedResource : resource));

      // Show success message
      setFormSuccess(t("admin.resource.updated.successfully", "Resource updated successfully!"));

      // Close modal after a delay
      setTimeout(() => {
        setIsEditModalOpen(false);
        setSelectedResource(null);
        resetForm();
      }, 1500);
    } catch (error) {
      console.error('Error updating resource:', error);
      setFormError(t("admin.failed.to.update", "Failed to update resource. Please try again."));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Confirm delete resource
  const confirmDeleteResource = async () => {
    if (selectedResource) {
      setFormSubmitting(true);
      try {
        // Call the API to delete the resource
        await resourcesAPI.deleteResource(selectedResource.id);
        // Update the local state
        setResources(resources.filter(resource => resource.id !== selectedResource.id));
        setIsDeleteModalOpen(false);
        setSelectedResource(null);
      } catch (error) {
        console.error('Error deleting resource:', error);
        // Show error message to user
        alert('Failed to delete resource. Please try again.');
      } finally {
        setFormSubmitting(false);
      }
    }
  };

  // Prevent default form submission
  const preventFormSubmission = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  };

  // Load empty data as fallback
  const loadMockData = () => {
    setLoading(false);
    setFormError(null);
    setResources([]);
  };

  return (
    <div className="p-6">
      <div onSubmit={preventFormSubmission} className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold">{t('admin.resourcesManagement')}</h1>
          <div className="text-gray-400 mt-1">{t('admin.manageLearningResources')}</div>
        </div>
        <button
          onClick={openCreateModal}
          className={`mt-4 sm:mt-0 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Plus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          {t('admin.addNewResource')}
        </button>
      </div>

      <div className="bg-indigo-900/20 rounded-xl overflow-hidden backdrop-blur-sm shadow-lg">
        <div className="p-4 border-b border-indigo-800/50">
          <div className={`flex flex-col md:flex-row md:items-center md:space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`relative flex-1 mb-4 md:mb-0 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={t('common.searchResources')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
              />
            </div>
            <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setFilter('all')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'all' ? 'bg-purple-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                {t('common.all')}
              </button>
              <button
                onClick={() => setFilter('article')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'article' ? 'bg-blue-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                {t('resources.types.articles')}
              </button>
              <button
                onClick={() => setFilter('video')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'video' ? 'bg-red-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                {t('resources.types.videos')}
              </button>
              <button
                onClick={() => setFilter('course')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'course' ? 'bg-green-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                {t('resources.types.courses')}
              </button>
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  fetchResources(0); // Start with a fresh fetch attempt
                }}
                className={`px-3 py-1.5 rounded-lg text-sm bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                disabled={loading}
              >
                <RefreshCw size={14} className={`mr-1 ${loading ? 'animate-spin' : ''}`} />
                {t('common.refresh')}
              </button>
            </div>
          </div>
        </div>

        {formError && (
          <div className="p-6">
            <div className="p-4 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
              {formError}
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  fetchResources(0);
                }}
                className={`ml-4 px-3 py-1 bg-red-700 hover:bg-red-600 rounded-lg text-white text-sm ${isRTL ? "space-x-reverse" : ""}`}
              >
                {t('common.tryAgain')}
              </button>
            </div>
          </div>
        )}

        {loading ? (
          <div className={`flex flex-col justify-center items-center py-20 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-6"></div>
            <div className="text-gray-400 mb-4">{t('common.loadingResources')}</div>
            <button
              type="button"
              onClick={loadMockData}
              className="px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white"
            >
              {t('common.showSampleDataInstead')}
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
            {filteredResources.length === 0 && !formError ? (
              <div className="col-span-full text-center py-10">
                <div className="text-gray-400 text-lg">{t('resources.noResourcesFound')}</div>
              </div>
            ) : (
              filteredResources.map((resource) => (
                <div key={resource.id} className="bg-indigo-900/30 rounded-xl overflow-hidden shadow-lg hover:shadow-purple-500/10 transition-all duration-300">
                  <div className="p-5">
                    <div className={`flex justify-between items-start mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${getResourceTypeColor(resource.resource_type)}`}>
                        {getResourceTypeLabel(resource.resource_type)}
                      </span>
                      <div className={`flex space-x-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <button
                          onClick={() => handleEditResource(resource)}
                          className="p-1.5 bg-indigo-800/80 rounded-full text-white hover:bg-indigo-700/80"
                          aria-label={t('common.edit')}
                          title={t('common.edit')}
                        >
                          <Edit size={14} />
                        </button>
                        <button
                          onClick={() => handleDeleteResource(resource)}
                          className="p-1.5 bg-red-800/80 rounded-full text-white hover:bg-red-700/80"
                          aria-label={t('common.delete')}
                          title={t('common.delete')}
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold mb-2">{resource.title}</h3>
                    <div className="text-gray-400 text-sm mb-4 line-clamp-2">{resource.description}</div>
                    <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <BookOpen size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{t('common.by')} {resource.author.username}</span>
                      </div>
                      <a
                        href={resource.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`flex items-center text-sm text-purple-400 hover:text-purple-300 ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        <ExternalLink size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        {t('resources.viewResource')}
                      </a>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedResource && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-md w-full">
            <h3 className="text-xl font-semibold mb-4">{t("admin.confirm.deletion", "Confirm Deletion")}</h3>
            <div className="text-gray-300 mb-6">
              Are you sure you want to delete the resource <span className="font-medium">{selectedResource.title}</span>? This action cannot be undone.
            </div>
            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                disabled={formSubmitting}
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteResource}
                className={`px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                disabled={formSubmitting}
              >
                {formSubmitting ? (
                  <>
                    <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    Delete
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Resource Modal */}
      {isCreateModalOpen && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full my-8">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-xl font-semibold">{t('admin.addNewResource')}</h3>
              <button
                onClick={() => setIsCreateModalOpen(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {formError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
                {formError}
              </div>
            )}

            {formSuccess && (
              <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-lg text-green-200">
                {formSuccess}
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">{t('admin.resourceTitle')} *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t('admin.enterResourceTitle')}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t('admin.resourceDescription')} *</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white h-32"
                  placeholder={t('admin.enterResourceDescription')}
                  required
                ></textarea>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t('admin.resourceType')} *</label>
                <select
                  name="resource_type"
                  value={formData.resource_type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  required
                >
                  <option value="article">{t('resources.types.article')}</option>
                  <option value="video">{t('resources.types.video')}</option>
                  <option value="course">{t('resources.types.course')}</option>
                  <option value="book">{t('resources.types.book')}</option>
                  <option value="tool">{t('resources.types.tool')}</option>
                  <option value="other">{t('resources.types.other')}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t('admin.resourceUrl')} *</label>
                <input
                  type="url"
                  name="url"
                  value={formData.url}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.resource.url", "Enter resource URL")}
                  required
                />
              </div>

              <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                  disabled={formSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={createResource}
                  className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                  disabled={formSubmitting}
                >
                  {formSubmitting ? (
                    <>
                      <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Create Resource
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Resource Modal */}
      {isEditModalOpen && selectedResource && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full my-8">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-xl font-semibold">t("admin.edit.resource", "Edit Resource")</h3>
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {formError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
                {formError}
              </div>
            )}

            {formSuccess && (
              <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-lg text-green-200">
                {formSuccess}
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Resource Title *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.resource.title", "Enter resource title")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Description *</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white h-32"
                  placeholder={t("admin.enter.resource.description", "Enter resource description")}
                  required
                ></textarea>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Resource Type *</label>
                <select
                  name="resource_type"
                  value={formData.resource_type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  required
                >
                  <option value="article">t("admin.article", "Article")</option>
                  <option value="video">t("admin.video", "Video")</option>
                  <option value="course">t("admin.course", "Course")</option>
                  <option value="book">t("admin.book", "Book")</option>
                  <option value="tool">t("admin.tool", "Tool")</option>
                  <option value="other">t("admin.other", "Other")</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">URL *</label>
                <input
                  type="url"
                  name="url"
                  value={formData.url}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.resource.url", "Enter resource URL")}
                  required
                />
              </div>

              <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsEditModalOpen(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                  disabled={formSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={updateResource}
                  className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                  disabled={formSubmitting}
                >
                  {formSubmitting ? (
                    <>
                      <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                      Updating...
                    </>
                  ) : (
                    <>
                      <Check size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Update Resource
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResourcesManagement;
