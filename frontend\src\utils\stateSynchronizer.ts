/**
 * Enhanced State Synchronizer
 * Provides comprehensive synchronization between localStorage and Redux auth state
 */

import { store } from '../store';
import { clearAuth, setAuthState, syncAuthWithTokens, forceAuthStateUpdate } from '../store/authSlice';
import { getAuthToken, getRefreshToken, clearAuthTokens, isTokenValid, getTokenExpiry } from '../services/api';
import { clearAuthStatusCache } from '../hooks/useAuthStatus';

interface SyncResult {
  success: boolean;
  action: string;
  details: string;
  timestamp: number;
}

interface StateConsistencyReport {
  isConsistent: boolean;
  issues: string[];
  recommendations: string[];
  syncResults: SyncResult[];
}

class StateSynchronizer {
  private syncHistory: SyncResult[] = [];
  private maxHistorySize = 50;
  private lastSyncTime = 0;
  private syncDebounceMs = 100;

  /**
   * Perform comprehensive state synchronization
   */
  async synchronizeState(): Promise<StateConsistencyReport> {
    const now = Date.now();
    
    // Debounce rapid sync calls
    if (now - this.lastSyncTime < this.syncDebounceMs) {
      return this.getLastReport();
    }
    
    this.lastSyncTime = now;
    
    console.log('🔄 Starting comprehensive state synchronization...');
    
    const report: StateConsistencyReport = {
      isConsistent: true,
      issues: [],
      recommendations: [],
      syncResults: []
    };

    try {
      // 1. Check token presence and validity
      const tokenResult = await this.syncTokenState();
      report.syncResults.push(tokenResult);
      
      // 2. Check Redux state consistency
      const reduxResult = await this.syncReduxState();
      report.syncResults.push(reduxResult);
      
      // 3. Check auth status cache consistency
      const cacheResult = await this.syncAuthCache();
      report.syncResults.push(cacheResult);
      
      // 4. Validate overall consistency
      const consistencyResult = await this.validateConsistency();
      report.syncResults.push(consistencyResult);
      
      // Analyze results
      report.isConsistent = report.syncResults.every(result => result.success);
      
      if (!report.isConsistent) {
        report.issues = report.syncResults
          .filter(result => !result.success)
          .map(result => result.details);
        
        report.recommendations = this.generateRecommendations(report.syncResults);
      }
      
      // Store sync history
      this.syncHistory.push(...report.syncResults);
      if (this.syncHistory.length > this.maxHistorySize) {
        this.syncHistory = this.syncHistory.slice(-this.maxHistorySize);
      }
      
      console.log(`✅ State synchronization completed. Consistent: ${report.isConsistent}`);
      
    } catch (error) {
      console.error('❌ State synchronization failed:', error);
      report.isConsistent = false;
      report.issues.push(`Synchronization error: ${error}`);
      report.recommendations.push('Retry synchronization or clear auth state');
    }
    
    return report;
  }

  /**
   * Synchronize token state
   */
  private async syncTokenState(): Promise<SyncResult> {
    const startTime = Date.now();
    
    try {
      const token = getAuthToken();
      const refreshToken = getRefreshToken();
      
      if (!token && !refreshToken) {
        return {
          success: true,
          action: 'token_check',
          details: 'No tokens found - user not authenticated',
          timestamp: startTime
        };
      }
      
      if (!token || !refreshToken) {
        // Inconsistent token state - clear all
        clearAuthTokens();
        return {
          success: false,
          action: 'token_cleanup',
          details: 'Inconsistent token state - cleared all tokens',
          timestamp: startTime
        };
      }
      
      // Validate token
      const isValid = isTokenValid();
      if (!isValid) {
        clearAuthTokens();
        return {
          success: false,
          action: 'token_validation',
          details: 'Invalid or expired tokens - cleared all tokens',
          timestamp: startTime
        };
      }
      
      return {
        success: true,
        action: 'token_validation',
        details: 'Tokens are valid and consistent',
        timestamp: startTime
      };
      
    } catch (error) {
      return {
        success: false,
        action: 'token_error',
        details: `Token sync error: ${error}`,
        timestamp: startTime
      };
    }
  }

  /**
   * Synchronize Redux state
   */
  private async syncReduxState(): Promise<SyncResult> {
    const startTime = Date.now();
    
    try {
      const state = store.getState();
      const authState = state.auth;
      const token = getAuthToken();
      const refreshToken = getRefreshToken();
      
      // Check for state inconsistencies
      if (authState.isAuthenticated && (!token || !refreshToken)) {
        // Redux says authenticated but no tokens
        store.dispatch(clearAuth());
        return {
          success: true,
          action: 'redux_cleanup',
          details: 'Cleared Redux auth state - no tokens found',
          timestamp: startTime
        };
      }
      
      if (!authState.isAuthenticated && token && refreshToken && isTokenValid()) {
        // Tokens exist but Redux not authenticated
        store.dispatch(syncAuthWithTokens());
        return {
          success: true,
          action: 'redux_sync',
          details: 'Synchronized Redux state with valid tokens',
          timestamp: startTime
        };
      }
      
      return {
        success: true,
        action: 'redux_check',
        details: 'Redux state is consistent',
        timestamp: startTime
      };
      
    } catch (error) {
      return {
        success: false,
        action: 'redux_error',
        details: `Redux sync error: ${error}`,
        timestamp: startTime
      };
    }
  }

  /**
   * Synchronize auth status cache
   */
  private async syncAuthCache(): Promise<SyncResult> {
    const startTime = Date.now();
    
    try {
      // Clear auth status cache to force refresh
      clearAuthStatusCache();
      
      return {
        success: true,
        action: 'cache_clear',
        details: 'Auth status cache cleared for fresh state',
        timestamp: startTime
      };
      
    } catch (error) {
      return {
        success: false,
        action: 'cache_error',
        details: `Cache sync error: ${error}`,
        timestamp: startTime
      };
    }
  }

  /**
   * Validate overall consistency
   */
  private async validateConsistency(): Promise<SyncResult> {
    const startTime = Date.now();
    
    try {
      const state = store.getState();
      const authState = state.auth;
      const token = getAuthToken();
      const refreshToken = getRefreshToken();
      const isValid = token ? isTokenValid() : false;
      
      const hasTokens = !!(token && refreshToken);
      const isAuthenticated = authState.isAuthenticated;
      const hasUser = !!authState.user;
      
      // Define consistency rules
      const rules = [
        {
          condition: hasTokens === isAuthenticated,
          message: 'Token presence matches authentication state'
        },
        {
          condition: !isAuthenticated || hasUser,
          message: 'Authenticated state has user data'
        },
        {
          condition: !hasTokens || isValid,
          message: 'Existing tokens are valid'
        }
      ];
      
      const failedRules = rules.filter(rule => !rule.condition);
      
      if (failedRules.length > 0) {
        return {
          success: false,
          action: 'consistency_check',
          details: `Failed rules: ${failedRules.map(r => r.message).join(', ')}`,
          timestamp: startTime
        };
      }
      
      return {
        success: true,
        action: 'consistency_check',
        details: 'All consistency rules passed',
        timestamp: startTime
      };
      
    } catch (error) {
      return {
        success: false,
        action: 'consistency_error',
        details: `Consistency check error: ${error}`,
        timestamp: startTime
      };
    }
  }

  /**
   * Generate recommendations based on sync results
   */
  private generateRecommendations(results: SyncResult[]): string[] {
    const recommendations: string[] = [];
    
    const failedResults = results.filter(r => !r.success);
    
    if (failedResults.some(r => r.action.includes('token'))) {
      recommendations.push('Clear all authentication data and re-login');
    }
    
    if (failedResults.some(r => r.action.includes('redux'))) {
      recommendations.push('Refresh the page to reset Redux state');
    }
    
    if (failedResults.some(r => r.action.includes('consistency'))) {
      recommendations.push('Perform manual state synchronization');
    }
    
    return recommendations;
  }

  /**
   * Get the last synchronization report
   */
  private getLastReport(): StateConsistencyReport {
    const recentResults = this.syncHistory.slice(-4);
    
    return {
      isConsistent: recentResults.every(r => r.success),
      issues: recentResults.filter(r => !r.success).map(r => r.details),
      recommendations: [],
      syncResults: recentResults
    };
  }

  /**
   * Get synchronization history
   */
  getSyncHistory(): SyncResult[] {
    return [...this.syncHistory];
  }

  /**
   * Clear synchronization history
   */
  clearHistory(): void {
    this.syncHistory = [];
  }

  /**
   * Force immediate synchronization (bypasses debouncing)
   */
  async forceSynchronization(): Promise<StateConsistencyReport> {
    this.lastSyncTime = 0;
    return this.synchronizeState();
  }
}

// Export singleton instance
export const stateSynchronizer = new StateSynchronizer();

// Export for console debugging
if (typeof window !== 'undefined') {
  (window as any).stateSynchronizer = stateSynchronizer;
  console.log('🔄 State synchronizer loaded. Use stateSynchronizer in console.');
}

export default stateSynchronizer;
