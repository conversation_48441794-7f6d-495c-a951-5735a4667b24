import React, { useState, useEffect } from 'react';
import { useAppSelector } from '../../store/hooks';
import { reduxDiagnostic } from '../../utils/reduxMigrationDiagnostic';
import { ReduxMigrationFixes } from '../../utils/reduxMigrationFixes';

interface DiagnosticResult {
  test: string;
  passed: boolean;
  message: string;
  details?: any;
}

/**
 * Debug component for Redux migration issues
 * Only shows in development mode
 */
const ReduxMigrationDebug: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [diagnosticResults, setDiagnosticResults] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  
  // Get current Redux state for display
  const authState = useAppSelector(state => state.auth);
  const aiContextState = useAppSelector(state => state.aiContext);
  const dashboardState = useAppSelector(state => state.dashboard);
  const toastState = useAppSelector(state => state.toast);
  const uiState = useAppSelector(state => state.ui);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const runDiagnostics = async () => {
    setIsRunning(true);
    try {
      const results = await reduxDiagnostic.runDiagnostics();
      setDiagnosticResults(results);
    } catch (error) {
      console.error('Error running diagnostics:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const runQuickFix = () => {
    ReduxMigrationFixes.quickFix();
    // Re-run diagnostics after fix
    setTimeout(runDiagnostics, 1000);
  };

  const runAllFixes = () => {
    ReduxMigrationFixes.runAllFixes();
    // Re-run diagnostics after fix
    setTimeout(runDiagnostics, 1000);
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg shadow-lg text-sm"
        >
          Redux Debug
        </button>
      </div>
    );
  }

  return (
    <div className="fixed inset-4 z-50 bg-gray-900 bg-opacity-95 backdrop-blur-sm rounded-lg overflow-hidden">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="bg-purple-600 text-white p-4 flex justify-between items-center">
          <h2 className="text-lg font-semibold">Redux Migration Debug</h2>
          <button
            onClick={() => setIsVisible(false)}
            className="text-white hover:text-gray-300"
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4 space-y-6">
          
          {/* Actions */}
          <div className="space-y-2">
            <h3 className="text-white font-medium">Actions</h3>
            <div className="flex space-x-2">
              <button
                onClick={runDiagnostics}
                disabled={isRunning}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-3 py-1 rounded text-sm"
              >
                {isRunning ? 'Running...' : 'Run Diagnostics'}
              </button>
              <button
                onClick={runQuickFix}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
              >
                Quick Fix
              </button>
              <button
                onClick={runAllFixes}
                className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-sm"
              >
                Run All Fixes
              </button>
            </div>
          </div>

          {/* Diagnostic Results */}
          {diagnosticResults.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-white font-medium">Diagnostic Results</h3>
              <div className="bg-gray-800 rounded p-3 max-h-40 overflow-auto">
                {diagnosticResults.map((result, index) => (
                  <div key={index} className={`text-sm mb-2 ${result.passed ? 'text-green-400' : 'text-red-400'}`}>
                    <div>{result.message}</div>
                    {!result.passed && result.details && (
                      <div className="text-gray-400 text-xs ml-4 mt-1">
                        {JSON.stringify(result.details, null, 2)}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Current State Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            
            {/* Auth State */}
            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-white font-medium mb-2">Auth State</h4>
              <div className="text-sm text-gray-300 space-y-1">
                <div>Authenticated: {authState.isAuthenticated ? '✅' : '❌'}</div>
                <div>User: {authState.user ? '✅' : '❌'}</div>
                <div>Loading: {authState.isLoading ? '⏳' : '✅'}</div>
                <div>Error: {authState.error ? '❌' : '✅'}</div>
              </div>
            </div>

            {/* AI Context State */}
            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-white font-medium mb-2">AI Context</h4>
              <div className="text-sm text-gray-300 space-y-1">
                <div>Current Page: {aiContextState.currentPage || 'None'}</div>
                <div>User ID: {aiContextState.userId || 'None'}</div>
                <div>AI Enabled: {aiContextState.isAIEnabled ? '✅' : '❌'}</div>
                <div>Help Items: {aiContextState.contextualHelp.length}</div>
              </div>
            </div>

            {/* Dashboard State */}
            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-white font-medium mb-2">Dashboard</h4>
              <div className="text-sm text-gray-300 space-y-1">
                <div>Role: {dashboardState.role}</div>
                <div>Stats: {dashboardState.stats.length}</div>
                <div>Loading: {dashboardState.loading.global ? '⏳' : '✅'}</div>
                <div>Errors: {dashboardState.errors.length}</div>
              </div>
            </div>

            {/* Toast State */}
            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-white font-medium mb-2">Toast</h4>
              <div className="text-sm text-gray-300 space-y-1">
                <div>Active Toasts: {toastState.toasts.length}</div>
              </div>
            </div>

            {/* UI State */}
            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-white font-medium mb-2">UI</h4>
              <div className="text-sm text-gray-300 space-y-1">
                <div>Tab Groups: {Object.keys(uiState.tabs).length}</div>
                <div>Modals: {Object.keys(uiState.modals).length}</div>
                <div>Sidebar: {uiState.sidebarOpen ? 'Open' : 'Closed'}</div>
                <div>Theme: {uiState.theme}</div>
              </div>
            </div>

          </div>

          {/* Instructions */}
          <div className="bg-gray-800 rounded p-3">
            <h4 className="text-white font-medium mb-2">Instructions</h4>
            <div className="text-sm text-gray-300 space-y-1">
              <div>1. Click "Run Diagnostics" to check for issues</div>
              <div>2. Use "Quick Fix" for common problems</div>
              <div>3. Use "Run All Fixes" for comprehensive repair</div>
              <div>4. Check the console for detailed logs</div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default ReduxMigrationDebug;
