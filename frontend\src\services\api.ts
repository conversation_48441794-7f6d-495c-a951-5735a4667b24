// API base URL - use environment variable if available
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// Import API call tracker for performance monitoring
import { apiCallTracker } from '../utils/apiCallTracker';

// Token management
const TOKEN_KEY = 'yasmeen_auth_token';
const REFRESH_TOKEN_KEY = 'yasmeen_refresh_token';

// Enhanced token caching with validation cache
interface TokenCache {
  accessToken: string | null;
  refreshToken: string | null;
  lastUpdated: number;
  cacheValidMs: number;
  // Validation cache
  isValid: boolean | null;
  validationTimestamp: number;
  validationCacheMs: number;
  tokenExpiry: number | null;
}

const tokenCache: TokenCache = {
  accessToken: null,
  refreshToken: null,
  lastUpdated: 0,
  cacheValidMs: 1000, // Cache tokens for 1 second to reduce localStorage calls
  // Validation cache
  isValid: null,
  validationTimestamp: 0,
  validationCacheMs: 5000, // Cache validation results for 5 seconds
  tokenExpiry: null,
};

// Export API constants for direct import
export const api = {
  API_URL,
  TOKEN_KEY,
  REFRESH_TOKEN_KEY,
  get: async <T>(endpoint: string, params?: Record<string, any>): Promise<T> => {
    const url = new URL(`${API_URL}${endpoint}`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value.toString());
        }
      });
    }
    return apiRequest<T>(url.pathname + url.search);
  },
  post: async <T>(endpoint: string, data?: any): Promise<T> => {
    return apiRequest<T>(endpoint, 'POST', data);
  },
  put: async <T>(endpoint: string, data?: any): Promise<T> => {
    return apiRequest<T>(endpoint, 'PUT', data);
  },
  delete: async <T>(endpoint: string): Promise<T> => {
    return apiRequest<T>(endpoint, 'DELETE');
  }
};

/**
 * Invalidate token cache completely
 */
function invalidateTokenCache(): void {
  tokenCache.accessToken = null;
  tokenCache.refreshToken = null;
  tokenCache.lastUpdated = 0;
  tokenCache.isValid = null;
  tokenCache.validationTimestamp = 0;
  tokenCache.tokenExpiry = null;
}

/**
 * Invalidate only validation cache (keep tokens cached)
 */
function invalidateValidationCache(): void {
  tokenCache.isValid = null;
  tokenCache.validationTimestamp = 0;
}

/**
 * Check if token cache is valid
 */
function isTokenCacheValid(): boolean {
  const now = Date.now();
  return (now - tokenCache.lastUpdated) < tokenCache.cacheValidMs;
}

/**
 * Update token cache
 */
function updateTokenCache(accessToken: string | null, refreshToken: string | null): void {
  const now = Date.now();
  tokenCache.accessToken = accessToken;
  tokenCache.refreshToken = refreshToken;
  tokenCache.lastUpdated = now;

  // Parse token expiry if available
  if (accessToken) {
    try {
      const payload = JSON.parse(atob(accessToken.split('.')[1]));
      tokenCache.tokenExpiry = payload.exp * 1000; // Convert to milliseconds
    } catch (error) {
      tokenCache.tokenExpiry = null;
    }
  } else {
    tokenCache.tokenExpiry = null;
  }

  // Invalidate validation cache when tokens change
  invalidateValidationCache();
}

// Get stored tokens with caching to reduce localStorage access
export function getAuthToken(): string | null {
  // Use cache if valid
  if (isTokenCacheValid() && tokenCache.accessToken !== null) {
    return tokenCache.accessToken;
  }

  // Fetch from localStorage and update cache
  const token = localStorage.getItem(TOKEN_KEY);
  updateTokenCache(token, tokenCache.refreshToken);

  // Only log in development to reduce console spam
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 getAuthToken called:', {
      tokenKey: TOKEN_KEY,
      hasToken: !!token,
      tokenLength: token?.length || 0,
      fromCache: false
    });
  }

  return token;
}

export function getRefreshToken(): string | null {
  // Use cache if valid
  if (isTokenCacheValid() && tokenCache.refreshToken !== null) {
    return tokenCache.refreshToken;
  }

  // Fetch from localStorage and update cache
  const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
  updateTokenCache(tokenCache.accessToken, refreshToken);

  return refreshToken;
}

// Store tokens and update cache
export function setAuthTokens(accessToken: string, refreshToken: string): void {
  localStorage.setItem(TOKEN_KEY, accessToken);
  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);

  // Update cache immediately
  updateTokenCache(accessToken, refreshToken);
}

// Clear tokens with debugging and cache invalidation
export function clearAuthTokens(): void {
  console.log('🗑️ clearAuthTokens called:', {
    hadToken: !!localStorage.getItem(TOKEN_KEY),
    hadRefresh: !!localStorage.getItem(REFRESH_TOKEN_KEY),
    stack: new Error().stack?.split('\n').slice(1, 4).join('\n')
  });

  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);

  // Invalidate cache
  invalidateTokenCache();

  // Clear auth status cache
  try {
    import('../hooks/useAuthStatus').then(({ clearAuthStatusCache }) => {
      clearAuthStatusCache();
    }).catch(() => {
      // Hook might not be available in all contexts
    });
  } catch (error) {
    // Hook might not be available in all contexts
  }
}

/**
 * Fast token validation using cache
 * Returns cached result if available, otherwise performs validation
 */
export function isTokenValid(): boolean {
  const now = Date.now();

  // Return cached validation result if still valid
  if (tokenCache.isValid !== null &&
      (now - tokenCache.validationTimestamp) < tokenCache.validationCacheMs) {
    return tokenCache.isValid;
  }

  // Get token (uses cache)
  const token = getAuthToken();
  if (!token) {
    tokenCache.isValid = false;
    tokenCache.validationTimestamp = now;
    return false;
  }

  // Check token expiry locally (fast validation)
  try {
    if (tokenCache.tokenExpiry) {
      const isExpired = now > tokenCache.tokenExpiry;
      tokenCache.isValid = !isExpired;
      tokenCache.validationTimestamp = now;
      return !isExpired;
    }

    // Fallback: parse token if expiry not cached
    const payload = JSON.parse(atob(token.split('.')[1]));
    const isExpired = payload.exp * 1000 < now;

    // Update cache
    tokenCache.tokenExpiry = payload.exp * 1000;
    tokenCache.isValid = !isExpired;
    tokenCache.validationTimestamp = now;

    return !isExpired;
  } catch (error) {
    // Invalid token format
    tokenCache.isValid = false;
    tokenCache.validationTimestamp = now;
    return false;
  }
}

/**
 * Get token expiry time in milliseconds
 */
export function getTokenExpiry(): number | null {
  const token = getAuthToken();
  if (!token) return null;

  if (tokenCache.tokenExpiry) {
    return tokenCache.tokenExpiry;
  }

  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expiry = payload.exp * 1000;
    tokenCache.tokenExpiry = expiry;
    return expiry;
  } catch (error) {
    return null;
  }
}

// CSRF Token (for session auth fallback)
export function getCsrfToken(): string | null {
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'csrftoken') {
      return value;
    }
  }
  return null;
}

// Extract error messages from Django REST Framework error responses
export function extractErrorMessage(errorData: any): string {
  if (!errorData) return 'An unknown error occurred';

  // If there's a non-field error
  if (errorData.non_field_errors) {
    return Array.isArray(errorData.non_field_errors)
      ? errorData.non_field_errors.join(', ')
      : errorData.non_field_errors;
  }

  // If there's a simple error message
  if (errorData.message) return errorData.message;
  if (errorData.detail) return errorData.detail;
  if (errorData.error) return errorData.error;

  // If there are field-specific errors
  const fieldErrors: string[] = [];
  for (const field in errorData) {
    if (Object.prototype.hasOwnProperty.call(errorData, field)) {
      const error = errorData[field];
      const errorMsg = Array.isArray(error) ? error.join(', ') : error;
      fieldErrors.push(`${field}: ${errorMsg}`);
    }
  }

  return fieldErrors.length > 0
    ? fieldErrors.join('; ')
    : 'An error occurred during the request';
}

// Types
export interface UserRole {
  id: number;
  name: 'super_admin' | 'admin' | 'moderator' | 'mentor' | 'investor' | 'user';
  display_name: string;
  description: string;
  permission_level: 'read' | 'write' | 'moderate' | 'admin' | 'super_admin';
  is_active: boolean;
  requires_approval: boolean;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  profile: UserProfile;
  is_admin: boolean;
  is_superuser?: boolean;
  is_active?: boolean;
  role?: string;
  bio?: string;
  phone_number?: string;
  location?: string;
  website?: string;
  github?: string;
  linkedin?: string;
  twitter?: string;
  date_joined?: string;
}

export interface UserProfile {
  id: number;
  bio: string;
  location: string;
  expertise: string;
  profile_image: string | null;
  website: string;
  github: string;
  linkedin: string;
  twitter: string;
  company: string;
  years_of_experience: number;
  primary_role: UserRole | null;
  active_roles: UserRole[];
  highest_permission_level: 'read' | 'write' | 'moderate' | 'admin' | 'super_admin';
  created_at: string;
  updated_at: string;
  completion_percentage: number;
}

export interface Event {
  id: number;
  title: string;
  description: string;
  date: string;
  location: string;
  is_virtual: boolean;
  virtual_link: string | null;
  image: string | null;
  organizer: User;
  attendees: User[];
  attendee_count: number;
  is_attending: boolean;
  moderation_status?: 'pending' | 'approved' | 'rejected';
  moderation_comment?: string;
  moderated_by?: User;
  moderated_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Tag {
  id: number;
  name: string;
  slug: string;
  description?: string;
}

export interface Resource {
  id: number;
  title: string;
  description: string;
  resource_type: string;
  url: string;
  image: string | null;
  author: User;
  tags: Tag[];
  duration?: string;
  created_at: string;
  updated_at: string;
}

export interface Post {
  id: number;
  title: string;
  excerpt: string;
  content: string;
  post_type: 'article' | 'discussion' | 'question' | 'announcement' | 'tutorial' | 'news';
  category: 'general' | 'business' | 'technology' | 'marketing' | 'finance' | 'legal' | 'operations' | 'product_development' | 'strategy' | 'networking';
  author: User;
  image: string | null;
  featured_image?: string | null;
  is_featured: boolean;
  is_published: boolean;
  allow_comments: boolean;
  like_count: number;
  is_liked: boolean;
  comments: Comment[];
  tags: Tag[];
  moderation_status?: 'pending' | 'approved' | 'rejected';
  moderation_comment?: string;
  moderated_by?: User;
  moderated_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Comment {
  id: number;
  post: number;
  author: User;
  content: string;
  moderation_status?: 'pending' | 'approved' | 'rejected';
  moderation_comment?: string;
  moderated_by?: User;
  moderated_at?: string;
  created_at: string;
  updated_at: string;
}

export interface MembershipApplication {
  id: number;
  user?: User;
  full_name: string;
  email: string;
  phone?: string;
  country: string;
  state?: string;
  location: string;
  expertise_areas: string;
  expertise_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  background: string;
  motivation: string;
  linkedin_profile?: string;
  github_profile?: string;
  portfolio_url?: string;
  status: 'pending' | 'approved' | 'rejected';
  reviewed_by?: User;
  reviewed_at?: string;
  review_notes?: string;
  created_at: string;
  updated_at: string;
}

// Error class for API errors
export class ApiError extends Error {
  status: number;
  data: any;

  constructor(message: string, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

// Helper function for refreshing the token
export async function refreshToken(): Promise<boolean> {
  const refreshToken = getRefreshToken();
  if (!refreshToken) return false;

  try {
    const response = await fetch(`${API_URL}/users/token/refresh/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh: refreshToken }),
    });

    if (!response.ok) {
      clearAuthTokens();
      return false;
    }

    const data = await response.json();
    if (data.access) {
      // Only update the access token, keep the refresh token
      localStorage.setItem(TOKEN_KEY, data.access);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Token refresh failed:', error);
    clearAuthTokens();
    return false;
  }
}

// Helper function for API requests
export async function apiRequest<T>(
  endpoint: string,
  method: string = 'GET',
  data?: any,
  withCredentials: boolean = true,
  retryWithRefresh: boolean = true
): Promise<T> {
  const url = `${API_URL}${endpoint}`;
  const startTime = Date.now();
  let tokenUsed = false;

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Add JWT token if available and credentials are required
  if (withCredentials) {
    const token = getAuthToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
      tokenUsed = true;
      console.log('🔐 Adding Bearer token to request:', endpoint);
    } else {
      console.log('⚠️ No token found for authenticated request:', endpoint);
      // For authenticated endpoints, we should have a token
      if (endpoint.includes('/incubator/') || endpoint.includes('/ai/') || endpoint.includes('/users/users/me')) {
        console.error('❌ Missing authentication token for protected endpoint:', endpoint);
      }
    }
    // Fallback to CSRF for non-GET requests if no JWT token
    if (!token && method !== 'GET') {
      const csrfToken = getCsrfToken();
      if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
      }
    }
  }

  const options: RequestInit = {
    method,
    headers,
    credentials: withCredentials ? 'include' : 'omit',
    body: data ? JSON.stringify(data) : undefined,
    mode: 'cors', // Explicitly set CORS mode
  };

  try {
    const response = await fetch(url, options);

    // Handle token expiration
    if (response.status === 401 && retryWithRefresh && withCredentials) {
      console.log('🔐 Received 401, attempting token refresh for:', endpoint);
      const token = getAuthToken();
      if (token) {
        const refreshed = await refreshToken();
        if (refreshed) {
          console.log('✅ Token refreshed, retrying request:', endpoint);
          // Retry the request with the new token
          return apiRequest<T>(endpoint, method, data, withCredentials, false);
        } else {
          console.log('❌ Token refresh failed, clearing tokens');
          clearAuthTokens();
        }
      } else {
        console.log('⚠️ No token found for 401 retry');
      }
    }

    if (!response.ok) {
      // Try to parse error response
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = {
          message: `API request failed with status ${response.status}`
        };
      }

      // Extract error message
      const errorMessage = extractErrorMessage(errorData);

      // Throw custom error with status and data
      throw new ApiError(
        errorMessage,
        response.status,
        errorData
      );
    }

    // For successful responses with no content
    if (response.status === 204) {
      return {} as T;
    }

    const result = await response.json();

    // Track successful API call
    const duration = Date.now() - startTime;
    apiCallTracker.trackCall(endpoint, method, duration, response.status, tokenUsed);

    return result;
  } catch (error) {
    // Track failed API call
    const duration = Date.now() - startTime;
    const status = error instanceof ApiError ? error.status : 0;
    apiCallTracker.trackCall(endpoint, method, duration, status, tokenUsed);

    if (error instanceof ApiError) {
      throw error;
    }
    console.error('API request error:', error);
    throw new ApiError(
      error instanceof Error ? error.message : 'Unknown error occurred',
      0,
      { originalError: error }
    );
  }
}

// Import mock auth service
import { MockAuthService } from './mockAuth';

// Authentication API
export interface TokenResponse {
  access: string;
  refresh: string;
}

export const authAPI = {
  // JWT token login
  login: async (username: string, password: string): Promise<User> => {
    // Try mock authentication first if backend is not available
    try {
      console.log('AuthAPI: Attempting to get JWT tokens for:', username);

      // Get JWT tokens
      const tokenResponse = await apiRequest<TokenResponse>(
        '/users/token/',
        'POST',
        { username, password },
        true,
        false // Don't retry with refresh for login
      );

      console.log('AuthAPI: JWT tokens received successfully');

      // Store tokens
      setAuthTokens(tokenResponse.access, tokenResponse.refresh);

      // Get user data
      console.log('AuthAPI: Fetching user data...');
      const user = await apiRequest<User>('/users/users/me/');
      console.log('AuthAPI: User data received:', user);

      // Check if user data is valid
      if (!user || typeof user !== 'object') {
        console.error('AuthAPI: Invalid user data received:', user);
        throw new Error('Invalid user data received from server');
      }

      // Ensure user object has expected structure and add defensive properties
      const sanitizedUser: User = {
        id: user.id || 0,
        username: user.username || '',
        email: user.email || '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        profile: user.profile || {
          id: 0,
          bio: '',
          location: '',
          expertise: '',
          profile_image: null,
          website: '',
          github: '',
          linkedin: '',
          twitter: '',
          company: '',
          years_of_experience: 0,
          primary_role: null,
          active_roles: [],
          highest_permission_level: 'read',
          created_at: '',
          updated_at: '',
          completion_percentage: 0
        },
        is_admin: user.is_admin || false,
        bio: user.bio || '',
        phone_number: user.phone_number || '',
        location: user.location || '',
        website: user.website || '',
        github: user.github || '',
        linkedin: user.linkedin || '',
        twitter: user.twitter || '',
        date_joined: user.date_joined || ''
      };

      console.log('AuthAPI: Sanitized user data:', sanitizedUser);
      return sanitizedUser;
    } catch (error: any) {
      console.error('AuthAPI: Login failed:', error);

      // Try mock authentication if backend fails
      if (error?.status >= 500 || error?.message?.includes('Failed to fetch')) {
        console.log('🧪 Backend unavailable, trying mock authentication...');
        MockAuthService.enableMockAuth();
        try {
          return await MockAuthService.login(username, password);
        } catch (mockError) {
          console.error('🧪 Mock authentication also failed:', mockError);
        }
      }

      // Clear any existing tokens on login failure
      clearAuthTokens();

      // Re-throw with more context
      if (error?.status === 401) {
        throw new Error('Invalid username or password');
      } else if (error?.status === 400) {
        throw new Error('Please check your username and password');
      } else if (error?.status >= 500) {
        throw new Error('Server error. Please try again later.');
      } else if (!navigator.onLine) {
        throw new Error('No internet connection. Please check your network.');
      }

      throw error;
    }
  },

  // JWT token refresh
  refreshToken: async (): Promise<boolean> => {
    return refreshToken();
  },

  // Logout - call backend and clear tokens
  logout: async (): Promise<{ message: string }> => {
    try {
      // Call backend logout endpoint (optional for JWT, but good practice)
      await apiRequest<{ message: string }>('/users/users/logout/', 'POST');
    } catch (error) {
      // Even if backend logout fails, we still want to clear local tokens
      console.warn('Backend logout failed, but clearing local tokens:', error);
    } finally {
      // Always clear local tokens
      clearAuthTokens();
    }
    return { message: "Logged out successfully" };
  },

  // Register new user
  register: async (userData: {
    username: string,
    email: string,
    password: string,
    password_confirm: string,
    first_name?: string,
    last_name?: string
  }): Promise<User> => {
    try {
      console.log('AuthAPI: Registering user:', userData.username);

      // Clear any existing tokens before registration
      clearAuthTokens();

      // Register user and get tokens in one request (no auth required)
      const response = await apiRequest<{
        user: User,
        access: string,
        refresh: string
      }>('/users/users/', 'POST', userData, false, false);

      // Store the tokens
      if (response.access && response.refresh) {
        setAuthTokens(response.access, response.refresh);
        console.log('Registration successful, tokens stored');
      } else {
        console.warn('Registration successful but no tokens received');
      }

      console.log('Registration successful, user:', response.user);

      // Check if user data is valid
      if (!response.user || typeof response.user !== 'object') {
        console.error('AuthAPI: Invalid user data received in registration:', response.user);
        throw new Error('Invalid user data received from server during registration');
      }

      // Ensure user object has expected structure and add defensive properties
      const sanitizedUser: User = {
        id: response.user.id || 0,
        username: response.user.username || '',
        email: response.user.email || '',
        first_name: response.user.first_name || '',
        last_name: response.user.last_name || '',
        profile: response.user.profile || {
          id: 0,
          bio: '',
          location: '',
          expertise: '',
          profile_image: null,
          website: '',
          github: '',
          linkedin: '',
          twitter: '',
          company: '',
          years_of_experience: 0,
          primary_role: null,
          active_roles: [],
          highest_permission_level: 'read',
          created_at: '',
          updated_at: '',
          completion_percentage: 0
        },
        is_admin: response.user.is_admin || false,
        bio: response.user.bio || '',
        phone_number: response.user.phone_number || '',
        location: response.user.location || '',
        website: response.user.website || '',
        github: response.user.github || '',
        linkedin: response.user.linkedin || '',
        twitter: response.user.twitter || '',
        date_joined: response.user.date_joined || ''
      };

      return sanitizedUser;
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  },

  // Get current user
  getCurrentUser: async (): Promise<User> => {
    try {
      const user = await apiRequest<User>('/users/users/me/');

    // Check if user data is valid
    if (!user || typeof user !== 'object') {
      console.error('AuthAPI: Invalid user data received in getCurrentUser:', user);
      throw new Error('Invalid user data received from server');
    }

    // Ensure user object has expected structure and add defensive properties
    const sanitizedUser: User = {
      id: user.id || 0,
      username: user.username || '',
      email: user.email || '',
      first_name: user.first_name || '',
      last_name: user.last_name || '',
      profile: user.profile || {
        id: 0,
        bio: '',
        location: '',
        expertise: '',
        profile_image: null,
        website: '',
        github: '',
        linkedin: '',
        twitter: '',
        company: '',
        years_of_experience: 0,
        primary_role: null,
        active_roles: [],
        highest_permission_level: 'read',
        created_at: '',
        updated_at: '',
        completion_percentage: 0
      },
      is_admin: user.is_admin || false,
      bio: user.bio || '',
      phone_number: user.phone_number || '',
      location: user.location || '',
      website: user.website || '',
      github: user.github || '',
      linkedin: user.linkedin || '',
      twitter: user.twitter || '',
      date_joined: user.date_joined || ''
    };

      return sanitizedUser;
    } catch (error: any) {
      console.error('AuthAPI: getCurrentUser failed:', error);

      // Try mock authentication if backend fails
      if (MockAuthService.isEnabled() || error?.status >= 500 || error?.message?.includes('Failed to fetch')) {
        console.log('🧪 Backend unavailable, trying mock getCurrentUser...');
        try {
          return await MockAuthService.getCurrentUser();
        } catch (mockError) {
          console.error('🧪 Mock getCurrentUser also failed:', mockError);
        }
      }

      throw error;
    }
  },

  // Get user profile
  getProfile: () =>
    apiRequest<UserProfile>('/users/profiles/my_profile/'),

  // Update user profile
  updateProfile: (profileData: Partial<UserProfile>) =>
    apiRequest<UserProfile>('/users/profiles/update_my_profile/', 'PUT', profileData),

  // Verify token is valid
  verifyToken: async (): Promise<boolean> => {
    const token = getAuthToken();
    if (!token) {
      console.log('No token to verify');
      return false;
    }

    try {
      // First check if token is expired locally
      const payload = JSON.parse(atob(token.split('.')[1]));
      const now = Math.floor(Date.now() / 1000);
      const isExpired = payload.exp < now;

      if (isExpired) {
        console.log('Token is expired locally, attempting refresh');
        return await refreshToken();
      }

      // Verify with server
      await apiRequest('/users/token/verify/', 'POST', { token }, true, false);
      return true;
    } catch (error) {
      console.error('Token verification failed:', error);
      if (error instanceof ApiError && error.status === 401) {
        // Check if mock auth is enabled
        if (MockAuthService.isEnabled()) {
          console.log('🧪 Backend verification failed but mock auth is enabled - checking mock token');
          try {
            await MockAuthService.getCurrentUser();
            console.log('🧪 Mock token verification successful');
            return true;
          } catch (mockError) {
            console.log('🧪 Mock token verification also failed');
            // Don't clear tokens immediately - let refresh attempt first
            return await refreshToken();
          }
        }

        // Try to refresh the token for real backend
        console.log('Token invalid, attempting refresh');
        return await refreshToken();
      }

      // Only clear tokens on non-401 errors and if not using mock auth
      if (!MockAuthService.isEnabled()) {
        clearAuthTokens();
      }
      return false;
    }
  }
};

// Interface for paginated responses
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Events API
export const eventsAPI = {
  getEvents: async () => {
    try {
      // Update the type to handle paginated response
      const response = await apiRequest<PaginatedResponse<Event>>('/events/');

      // Check if response has the expected structure
      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        console.log('Successfully extracted events from paginated response:', response.results);
        return response.results;
      } else {
        // If response is already an array (non-paginated)
        if (Array.isArray(response)) {
          return response;
        }

        console.error('API returned unexpected format:', response);
        return [];
      }
    } catch (error) {
      console.error('Error fetching events:', error);
      return [];
    }
  },

  getEvent: (id: number) =>
    apiRequest<Event>(`/events/${id}/`),

  createEvent: (eventData: Partial<Event> & { organizer_id: number }) =>
    apiRequest<Event>('/events/', 'POST', eventData),

  updateEvent: (id: number, eventData: Partial<Event>) =>
    apiRequest<Event>(`/events/${id}/`, 'PUT', eventData),

  deleteEvent: (id: number) =>
    apiRequest<void>(`/events/${id}/`, 'DELETE'),

  attendEvent: (id: number) =>
    apiRequest<{ message: string }>(`/events/${id}/attend/`, 'POST'),

  unattendEvent: (id: number) =>
    apiRequest<{ message: string }>(`/events/${id}/unattend/`, 'POST'),

  moderateEvent: (id: number, status: 'approved' | 'rejected', comment: string = '') =>
    apiRequest<{ message: string, event_id: number, event_title: string, moderation_status: string }>(
      `/events/${id}/moderate/`,
      'POST',
      { status, comment }
    ),
};

// Resources API
export const resourcesAPI = {
  getResources: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<Resource>>('/resources/');

      // Check if response has the expected structure
      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for resources:', response);
      return [];
    } catch (error) {
      console.error('Error fetching resources:', error);
      return [];
    }
  },

  getResource: (id: number) =>
    apiRequest<Resource>(`/resources/${id}/`),

  createResource: (resourceData: Partial<Resource> & { author_id: number }) =>
    apiRequest<Resource>('/resources/', 'POST', resourceData),

  updateResource: (id: number, resourceData: Partial<Resource>) =>
    apiRequest<Resource>(`/resources/${id}/`, 'PUT', resourceData),

  deleteResource: (id: number) =>
    apiRequest<void>(`/resources/${id}/`, 'DELETE'),
};

// Membership Applications API
export const membershipAPI = {
  getApplications: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<MembershipApplication>>('/membership-applications/');

      // Check if response has the expected structure
      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for membership applications:', response);
      return [];
    } catch (error) {
      console.error('Error fetching membership applications:', error);
      return [];
    }
  },

  getApplication: (id: number) =>
    apiRequest<MembershipApplication>(`/membership-applications/${id}/`),

  createApplication: (applicationData: Omit<MembershipApplication, 'id' | 'status' | 'reviewed_by' | 'reviewed_at' | 'review_notes' | 'created_at' | 'updated_at'>) =>
    apiRequest<MembershipApplication>('/membership-applications/', 'POST', applicationData),

  reviewApplication: (id: number, status: 'approved' | 'rejected', notes: string = '') =>
    apiRequest<{ message: string, application_id: number, applicant_name: string, status: string }>(
      `/membership-applications/${id}/review/`,
      'POST',
      { status, notes }
    ),
};

// Posts API
export const postsAPI = {
  getPosts: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<Post>>('/posts/');

      // Check if response has the expected structure
      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for posts:', response);
      return [];
    } catch (error) {
      console.error('Error fetching posts:', error);
      return [];
    }
  },

  getPost: (id: number) =>
    apiRequest<Post>(`/posts/${id}/`),

  createPost: (postData: Partial<Post> & { author_id: number }) =>
    apiRequest<Post>('/posts/', 'POST', postData),

  updatePost: (id: number, postData: Partial<Post>) =>
    apiRequest<Post>(`/posts/${id}/`, 'PUT', postData),

  deletePost: (id: number) =>
    apiRequest<void>(`/posts/${id}/`, 'DELETE'),

  likePost: (id: number) =>
    apiRequest<{ message: string }>(`/posts/${id}/like/`, 'POST'),

  unlikePost: (id: number) =>
    apiRequest<{ message: string }>(`/posts/${id}/unlike/`, 'POST'),

  moderatePost: (id: number, status: 'approved' | 'rejected', comment: string = '') =>
    apiRequest<{ message: string, post_id: number, post_title: string, moderation_status: string }>(
      `/posts/${id}/moderate/`,
      'POST',
      { status, comment }
    ),
};

// Comments API
export const commentsAPI = {
  getComments: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<Comment>>('/comments/');

      // Check if response has the expected structure
      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for comments:', response);
      return [];
    } catch (error) {
      console.error('Error fetching comments:', error);
      return [];
    }
  },

  createComment: (commentData: { post: number, author_id: number, content: string }) =>
    apiRequest<Comment>('/comments/', 'POST', commentData),

  updateComment: (id: number, commentData: { content: string }) =>
    apiRequest<Comment>(`/comments/${id}/`, 'PUT', commentData),

  deleteComment: (id: number) =>
    apiRequest<void>(`/comments/${id}/`, 'DELETE'),

  moderateComment: (id: number, status: 'approved' | 'rejected', comment: string = '') =>
    apiRequest<{ message: string, comment_id: number, comment_content: string, moderation_status: string }>(
      `/comments/${id}/moderate/`,
      'POST',
      { status, comment }
    ),
};

// Tags API
export const tagsAPI = {
  getTags: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<Tag>>('/tags/');

      // Check if response has the expected structure
      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for tags:', response);
      return [];
    } catch (error) {
      console.error('Error fetching tags:', error);
      return [];
    }
  },

  getTag: (id: number) =>
    apiRequest<Tag>(`/tags/${id}/`),

  createTag: (tagData: { name: string, description?: string }) =>
    apiRequest<Tag>('/tags/', 'POST', tagData),

  updateTag: (id: number, tagData: { name?: string, description?: string }) =>
    apiRequest<Tag>(`/tags/${id}/`, 'PUT', tagData),

  deleteTag: (id: number) =>
    apiRequest<void>(`/tags/${id}/`, 'DELETE'),
};

// User activity interface
export interface UserActivity {
  post_count: number;
  comment_count: number;
  event_count: number;
  resource_count: number;
  likes_received: number;
  engagement_score: number;
  posts_by_day: Record<string, number>;
  comments_by_day: Record<string, number>;
  recent_posts: Array<{
    id: number;
    title: string;
    created_at: string;
  }>;
  recent_comments: Array<{
    id: number;
    content: string;
    created_at: string;
  }>;
  recent_events: Array<{
    id: number;
    title: string;
    date: string;
  }>;
}

// Content recommendations interface
export interface ContentRecommendations {
  recommended_posts: Array<{
    id: number;
    title: string;
    author: string;
  }>;
  recommended_events: Array<{
    id: number;
    title: string;
    date: string;
    organizer: string;
  }>;
  recommended_resources: Array<{
    id: number;
    title: string;
    resource_type: string;
    author: string;
  }>;
}

// Business idea progress tracking interface
export interface BusinessIdeaProgress {
  total_updates: number;
  updates_by_month: Record<string, number>;
  update_frequency: number;
  recent_updates: Array<{
    id: number;
    title: string;
    created_at: string;
    created_by: string;
  }>;
  common_achievement_themes: Array<{
    word: string;
    count: number;
  }>;
  common_challenge_themes: Array<{
    word: string;
    count: number;
  }>;
  current_stage: string;
  idea_age_days: number;
}

// User API
export const userAPI = {
  // Get user profile
  getUserProfile: (userId: number) =>
    apiRequest<User>(`/users/users/${userId}/`),

  // Update user profile
  updateUserProfile: (profileData: {
    first_name?: string,
    last_name?: string,
    email?: string,
    bio?: string,
    location?: string,
    website?: string,
    linkedin?: string,
    twitter?: string,
    github?: string,
  }) =>
    apiRequest<UserProfile>('/users/profiles/update_my_profile/', 'PUT', profileData),

  // Get user activity statistics
  getUserActivity: async () => {
    try {
      return await apiRequest<UserActivity>('/users/users/user_activity/');
    } catch (error) {
      console.error('Error fetching user activity:', error);
      // Return default data on error
      return {
        post_count: 0,
        comment_count: 0,
        event_count: 0,
        resource_count: 0,
        likes_received: 0,
        engagement_score: 0,
        posts_by_day: {},
        comments_by_day: {},
        recent_posts: [],
        recent_comments: [],
        recent_events: []
      };
    }
  },

  // Get personalized content recommendations
  getContentRecommendations: async () => {
    try {
      // Temporarily disabled - endpoint not available
      // return await apiRequest<ContentRecommendations>('/users/content_recommendations/');
      console.log('Content recommendations endpoint temporarily disabled');
      // Return default data
      return {
        recommended_posts: [],
        recommended_events: [],
        recommended_resources: []
      };
    } catch (error) {
      console.error('Error fetching content recommendations:', error);
      // Return default data on error
      return {
        recommended_posts: [],
        recommended_events: [],
        recommended_resources: []
      };
    }
  },

  // Get forum activity
  getForumActivity: async () => {
    try {
      return await apiRequest<any>('/users/forum_activity/');
    } catch (error) {
      console.error('Error fetching forum activity:', error);
      // Return default data on error
      return {
        thread_count: 0,
        post_count: 0,
        likes_received: 0,
        solution_count: 0,
        reputation: {
          points: 0,
          level: 'Newcomer',
          threads_created: 0,
          posts_created: 0,
          solutions_provided: 0,
          likes_received: 0,
          likes_given: 0
        },
        recent_threads: [],
        recent_posts: [],
        recent_activities: [],
        threads_by_day: {},
        posts_by_day: {}
      };
    }
  },

  // Get all users (admin only)
  getUsers: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<User>>('/users/users/');

      // Check if response has the expected structure
      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for users:', response);
      return [];
    } catch (error) {
      console.error('Error fetching users:', error);
      return [];
    }
  },
};

// Admin API
export interface RecentActivity {
  type: 'user_joined' | 'event_created' | 'resource_shared' | 'post_created';
  user: string;
  timestamp: string;
}

export interface DashboardStats {
  users: {
    total_users: number;
    active_users: number;
    staff_users: number;
    superusers: number;
    new_users: number;
    user_growth_rate: number;
    users_by_month: Record<string, number>;
    users_by_location: Record<string, number>;
    users_by_expertise: Record<string, number>;
    engagement_rate: number;
    posts_per_user: number;
    comments_per_post: number;
    daily_active_users: number;
    weekly_active_users: number;
    monthly_active_users: number;
    retention_rate: number;
    most_active_users: Array<{
      id: number;
      username: string;
      post_count: number;
      comment_count: number;
      event_count: number;
      resource_count: number;
      likes_received: number;
      profile_completion: number;
      activity_score: number;
      last_active: string;
    }>;
    content_engagement_by_month: Record<string, {
      posts: number;
      comments: number;
      total: number;
    }>;
  };
  events: {
    total_events: number;
    upcoming_events: number;
    new_events: number;
    events_by_month: Record<string, number>;
    events_by_location: Record<string, number>;
    popular_events: Array<{
      id: number;
      title: string;
      date: string;
      location: string;
      attendee_count: number;
      organizer: string;
    }>;
  };
  resources: {
    total_resources: number;
    resources_by_type: Record<string, number>;
    new_resources: number;
  };
  posts: {
    total_posts: number;
    popular_posts: Array<{
      id: number;
      title: string;
      like_count: number;
      author: string;
    }>;
    new_posts: number;
  };
}

import { apiCache, cacheKeys } from '../utils/apiCache';

export const adminAPI = {
  getUserStats: async () => {
    return apiCache.get(
      cacheKeys.userStats(),
      async () => {
        try {
          // Check if user is authenticated first
          const token = getAuthToken();
          if (!token) {
            console.warn('No authentication token available for getUserStats');
            throw new Error('Authentication required');
          }

          return await apiRequest<DashboardStats['users']>('/users/users/admin_dashboard_stats/');
        } catch (error) {
      console.error('Error fetching user stats:', error);
      // Return realistic mock data on error
      return {
        total_users: 150,
        active_users: 120,
        staff_users: 5,
        superusers: 2,
        new_users: 25,
        user_growth_rate: 15.5,
        users_by_month: {
          '2024-01': 45,
          '2024-02': 62,
          '2024-03': 78,
          '2024-04': 95,
          '2024-05': 120,
          '2024-06': 150
        },
        users_by_location: {
          'Saudi Arabia': 85,
          'UAE': 35,
          'Egypt': 20,
          'Other': 10
        },
        users_by_expertise: {
          'Technology': 45,
          'Business': 35,
          'Healthcare': 25,
          'Education': 20,
          'Other': 25
        },
        engagement_rate: 78.5,
        posts_per_user: 3.2,
        comments_per_post: 5.8,
        daily_active_users: 45,
        weekly_active_users: 85,
        monthly_active_users: 120,
        retention_rate: 85.2,
        most_active_users: [
          { username: 'Ahmed_Tech', posts: 25, comments: 45 },
          { username: 'Sara_Business', posts: 18, comments: 32 },
          { username: 'Omar_Health', posts: 15, comments: 28 }
        ],
        content_engagement_by_month: {
          '2024-01': 65.2,
          '2024-02': 70.1,
          '2024-03': 72.8,
          '2024-04': 75.5,
          '2024-05': 77.2,
          '2024-06': 78.5
        }
      };
        }
      },
      3 // Cache for 3 minutes
    );
  },

  getEventStats: async () => {
    return apiCache.get(
      cacheKeys.eventStats(),
      async () => {
        try {
          const token = getAuthToken();
          if (!token) {
            console.warn('No authentication token available for getEventStats');
            throw new Error('Authentication required');
          }

          return await apiRequest<DashboardStats['events']>('/events/admin_stats/');
        } catch (error) {
      console.error('Error fetching event stats:', error);
      // Return realistic mock data on error
      return {
        total_events: 12,
        upcoming_events: 8,
        new_events: 3,
        events_by_month: {
          '2024-01': 2,
          '2024-02': 3,
          '2024-03': 4,
          '2024-04': 5,
          '2024-05': 6,
          '2024-06': 8
        },
        events_by_location: {
          'Riyadh': 5,
          'Dubai': 3,
          'Cairo': 2,
          'Online': 2
        },
        popular_events: [
          { title: 'Tech Startup Meetup', attendees: 45 },
          { title: 'Business Innovation Summit', attendees: 32 },
          { title: 'Healthcare Technology Conference', attendees: 28 }
        ]
      };
        }
      },
      3 // Cache for 3 minutes
    );
  },

  getResourceStats: async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('No authentication token available for getResourceStats');
        throw new Error('Authentication required');
      }

      return await apiRequest<DashboardStats['resources']>('/resources/admin_stats/');
    } catch (error) {
      console.error('Error fetching resource stats:', error);
      // Return realistic mock data on error
      return {
        total_resources: 28,
        resources_by_type: {
          'Articles': 12,
          'Videos': 8,
          'Documents': 5,
          'Tools': 3
        },
        new_resources: 5
      };
    }
  },

  getPostStats: async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('No authentication token available for getPostStats');
        throw new Error('Authentication required');
      }

      return await apiRequest<DashboardStats['posts']>('/posts/admin_stats/');
    } catch (error) {
      console.error('Error fetching post stats:', error);
      // Return realistic mock data on error
      return {
        total_posts: 45,
        popular_posts: [
          { title: 'Building Scalable Startups', likes: 25, comments: 12 },
          { title: 'AI in Healthcare Innovation', likes: 18, comments: 8 },
          { title: 'Sustainable Business Models', likes: 15, comments: 6 }
        ],
        new_posts: 8
      };
    }
  },

  getRecentActivity: async () => {
    try {
      return await apiRequest<RecentActivity[]>('/recent_activity/');
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      // Return empty array on error
      return [];
    }
  },

  getAllStats: async (): Promise<DashboardStats> => {
    const [users, events, resources, posts] = await Promise.all([
      adminAPI.getUserStats(),
      adminAPI.getEventStats(),
      adminAPI.getResourceStats(),
      adminAPI.getPostStats()
    ]);

    return {
      users,
      events,
      resources,
      posts
    };
  },

  getUsers: async () => {
    return apiCache.get(
      cacheKeys.users(),
      async () => {
        try {
          const token = getAuthToken();
          if (!token) {
            console.warn('No authentication token available for getUsers');
            throw new Error('Authentication required');
          }

          const response = await apiRequest<any>('/superadmin/users/users-management/');

          // Check if response has the expected structure from super admin API
          if (response && typeof response === 'object' && 'users' in response && Array.isArray(response.users)) {
            console.log('Successfully extracted users from super admin response:', response.users);
            return response.users;
          } else if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
            console.log('Successfully extracted users from paginated response:', response.results);
            return response.results;
          } else if (Array.isArray(response)) {
            return response;
          }

          console.error('API returned unexpected format for users:', response);
          return [];
        } catch (error) {
      console.error('Error fetching users:', error);
      // Return mock users data when API fails
      return [
        {
          id: 1,
          username: 'admin_user',
          email: '<EMAIL>',
          first_name: 'Admin',
          last_name: 'User',
          is_admin: true,
          is_superuser: true,
          date_joined: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          username: 'john_doe',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe',
          is_admin: false,
          is_superuser: false,
          date_joined: '2024-02-15T10:30:00Z'
        },
        {
          id: 3,
          username: 'jane_smith',
          email: '<EMAIL>',
          first_name: 'Jane',
          last_name: 'Smith',
          is_admin: false,
          is_superuser: false,
          date_joined: '2024-03-20T14:45:00Z'
        }
      ];
        }
      },
      5 // Cache users for 5 minutes
    );
  },

  getUser: (id: number) =>
    apiRequest<User>(`/users/users/${id}/`),

  createUser: (userData: {
    username: string,
    email: string,
    password: string,
    password_confirm: string,
    first_name?: string,
    last_name?: string,
    role?: string,
    is_active?: boolean
  }) =>
    apiRequest<User>('/users/users/', 'POST', userData),

  updateUser: (id: number, userData: {
    username?: string,
    email?: string,
    first_name?: string,
    last_name?: string,
    is_admin?: boolean
  }) =>
    apiRequest<User>('/superadmin/users/update-user/', 'POST', { user_id: id, user_data: userData }),

  updateUserProfile: (userId: number, profileData: Partial<UserProfile>) =>
    apiRequest<UserProfile>(`/users/profiles/${userId}/update/`, 'PUT', profileData),

  deleteUser: (id: number) =>
    apiRequest<void>('/superadmin/users/delete-user/', 'POST', { user_id: id }),
};

// Search API interfaces
export interface SearchResult {
  id: string;
  type: 'business' | 'forum' | 'resource' | 'user';
  title: string;
  description: string;
  score: number;
  metadata?: Record<string, any>;
}

export interface SearchFilters {
  types?: string[];
  dateRange?: string;
  author?: string;
}

export interface UniversalSearchResponse {
  query: string;
  total_results: number;
  results: SearchResult[];
  search_types: string[];
  filters: SearchFilters;
}

export interface SearchSuggestionsResponse {
  suggestions: string[];
}

// Search API
export const searchAPI = {
  // Universal search across all content types
  universalSearch: async (
    query: string,
    filters?: SearchFilters,
    limit?: number
  ): Promise<UniversalSearchResponse> => {
    const params: Record<string, any> = { q: query };

    if (filters?.types?.length) {
      params.types = filters.types.join(',');
    }
    if (filters?.dateRange) {
      params.dateRange = filters.dateRange;
    }
    if (filters?.author) {
      params.author = filters.author;
    }
    if (limit) {
      params.limit = limit;
    }

    return api.get<UniversalSearchResponse>('/search/universal/', params);
  },

  // Get search suggestions
  getSearchSuggestions: async (query: string): Promise<SearchSuggestionsResponse> => {
    return api.get<SearchSuggestionsResponse>('/search/suggestions/', { q: query });
  },

  // Search business ideas specifically
  searchBusinessIdeas: async (query: string, filters?: SearchFilters) => {
    const params: Record<string, any> = { q: query, types: 'business' };

    if (filters?.dateRange) {
      params.dateRange = filters.dateRange;
    }
    if (filters?.author) {
      params.author = filters.author;
    }

    return api.get<UniversalSearchResponse>('/search/universal/', params);
  },

  // Search forum content specifically
  searchForumContent: async (query: string, filters?: SearchFilters) => {
    const params: Record<string, any> = { q: query, types: 'forum' };

    if (filters?.dateRange) {
      params.dateRange = filters.dateRange;
    }
    if (filters?.author) {
      params.author = filters.author;
    }

    return api.get<UniversalSearchResponse>('/search/universal/', params);
  },

  // Search users specifically
  searchUsers: async (query: string, filters?: SearchFilters) => {
    const params: Record<string, any> = { q: query, types: 'user' };

    if (filters?.author) {
      params.author = filters.author;
    }

    return api.get<UniversalSearchResponse>('/search/universal/', params);
  },

  // Search resources specifically
  searchResources: async (query: string, filters?: SearchFilters) => {
    const params: Record<string, any> = { q: query, types: 'resource' };

    if (filters?.dateRange) {
      params.dateRange = filters.dateRange;
    }
    if (filters?.author) {
      params.author = filters.author;
    }

    return api.get<UniversalSearchResponse>('/search/universal/', params);
  }
};
