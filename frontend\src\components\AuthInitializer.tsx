import { useEffect } from 'react';
import { useAppDispatch } from '../store/hooks';
import { initializeAuth } from '../store/authSlice';
import { getAuthToken, getRefreshToken } from '../services/api';

const AuthInitializer = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Initialize authentication state on app startup
    const initAuth = async () => {
      console.log("🚀 Starting authentication initialization...");

      try {
        const resultAction = await dispatch(initializeAuth());

        if (initializeAuth.fulfilled.match(resultAction)) {
          console.log("✅ Authentication initialized successfully:", resultAction.payload.username);
        } else if (initializeAuth.rejected.match(resultAction)) {
          // This is expected when user is not logged in or tokens are invalid
          console.log("ℹ️ Authentication initialization completed (user not authenticated):", resultAction.payload);
        }
      } catch (error) {
        console.warn('⚠️ Unexpected error during auth initialization:', error);
      }
    };

    initAuth();
  }, [dispatch]);

  // This component doesn't render anything
  return null;
};

export default AuthInitializer;
