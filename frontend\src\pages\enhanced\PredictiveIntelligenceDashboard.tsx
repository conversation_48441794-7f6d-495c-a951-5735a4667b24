/**
 * Predictive Intelligence Dashboard
 * Central hub for all AI predictive capabilities
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Brain,
  TrendingUp,
  AlertTriangle,
  Target,
  Zap,
  BarChart3,
  Activity,
  Eye,
  RefreshCw,
  Settings,
  Download,
  Calendar,
  Award,
  Shield,
  Users,
  DollarSign
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
// MainLayout removed - handled by routing system
import { getAuthToken } from '../../services/api';
// Placeholder components - these would be implemented separately
const SuccessProbabilityEngine = ({ businessIdeaId, businessIdeaData, userId }: any) => (
  <div className="p-6 bg-gray-100 dark:bg-gray-800 rounded-lg">
    <h3 className="text-lg font-semibold mb-4">Success Probability Engine</h3>
    <p>Analyzing success probability for business idea {businessIdeaId}...</p>
  </div>
);

const RiskAssessmentDashboard = ({ businessIdeaId, businessIdeaData, userId }: any) => (
  <div className="p-6 bg-gray-100 dark:bg-gray-800 rounded-lg">
    <h3 className="text-lg font-semibold mb-4">Risk Assessment Dashboard</h3>
    <p>Assessing risks for business idea {businessIdeaId}...</p>
  </div>
);

const MarketTrendPrediction = ({ businessIdeaId, businessIdeaData, userId }: any) => (
  <div className="p-6 bg-gray-100 dark:bg-gray-800 rounded-lg">
    <h3 className="text-lg font-semibold mb-4">Market Trend Prediction</h3>
    <p>Predicting market trends for business idea {businessIdeaId}...</p>
  </div>
);

const PerformanceOptimizationEngine = ({ businessIdeaId, businessIdeaData, userId }: any) => (
  <div className="p-6 bg-gray-100 dark:bg-gray-800 rounded-lg">
    <h3 className="text-lg font-semibold mb-4">Performance Optimization Engine</h3>
    <p>Optimizing performance for business idea {businessIdeaId}...</p>
  </div>
);

const ProactiveAINotifications = ({ userId }: any) => (
  <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
    <span className="text-sm text-blue-700 dark:text-blue-300">AI Active</span>
  </div>
);

import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { updateContext } from '../../store/aiContextSlice';
import EnhancedPredictiveAnalyticsDashboard from '../../components/analytics/EnhancedPredictiveAnalyticsDashboard';

interface PredictiveMetrics {
  success_probability: number;
  risk_score: number;
  market_opportunity: number;
  optimization_potential: number;
  confidence_level: number;
  last_updated: Date;
}

const PredictiveIntelligenceDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  const [activeTab, setActiveTab] = useState<string>('overview');
  const [selectedBusinessIdea, setSelectedBusinessIdea] = useState<any>(null);
  const [metrics, setMetrics] = useState<PredictiveMetrics | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch real business ideas
  const [businessIdeas, setBusinessIdeas] = useState([]);

  const tabs = [
    { id: 'overview', name: 'Overview', icon: BarChart3 },
    { id: 'success_prediction', name: 'Success Prediction', icon: TrendingUp },
    { id: 'risk_assessment', name: 'Risk Assessment', icon: AlertTriangle },
    { id: 'market_trends', name: 'Market Trends', icon: Activity },
    { id: 'optimization', name: 'Performance Optimization', icon: Zap },
    { id: 'enhanced_analytics', name: 'Enhanced Analytics', icon: Brain },
    { id: 'failure_prediction', name: 'Failure Prediction', icon: Shield },
    { id: 'timing_optimization', name: 'Market Timing', icon: Target },
    { id: 'competitor_analysis', name: 'Competitor Analysis', icon: Users },
    { id: 'cac_prediction', name: 'CAC Prediction', icon: DollarSign }
  ];

  useEffect(() => {
    fetchBusinessIdeas();
    dispatch(updateContext({
      currentPage: 'predictive-intelligence-dashboard',
      currentContext: {
        pageType: 'predictive_intelligence',
        features: ['analytics', 'prediction', 'intelligence'],
        aiRelevance: 'very-high' as const,
        activeTab,
        selectedBusinessIdea
      }
    }));
  }, [dispatch, activeTab, selectedBusinessIdea]);

  const fetchBusinessIdeas = async () => {
    try {
      const response = await fetch('/api/incubator/business-ideas/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setBusinessIdeas(data.results || data || []);
      } else {
        console.error('Failed to fetch business ideas:', response.status);
        setBusinessIdeas([]);
      }
    } catch (error) {
      console.error('Failed to fetch business ideas:', error);
      setBusinessIdeas([]);
    }
  };

  useEffect(() => {
    if (selectedBusinessIdea) {
      generatePredictiveMetrics();
    }
  }, [selectedBusinessIdea]);

  const generatePredictiveMetrics = async () => {
    setIsRefreshing(true);
    try {
      const response = await fetch('/api/ai/predictive-metrics/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          business_ideas: businessIdeas,
          analysis_type: 'comprehensive'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setMetrics(data.metrics || null);
      } else {
        console.error('Failed to generate predictive metrics:', response.status);
        setMetrics(null);
      }
    } catch (error) {
      console.error('Failed to generate predictive metrics:', error);
      setMetrics(null);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getScoreColor = (score: number, reverse = false) => {
    if (reverse) {
      if (score <= 40) return 'text-green-600 dark:text-green-400';
      if (score <= 70) return 'text-yellow-600 dark:text-yellow-400';
      return 'text-red-600 dark:text-red-400';
    } else {
      if (score >= 80) return 'text-green-600 dark:text-green-400';
      if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
      return 'text-red-600 dark:text-red-400';
    }
  };

  const renderTabContent = () => {
    if (!selectedBusinessIdea) return null;

    // Convert business idea to BusinessData format for enhanced analytics
    const businessData = {
      title: selectedBusinessIdea.title,
      description: selectedBusinessIdea.description,
      stage: selectedBusinessIdea.stage === 'idea' ? 1 : selectedBusinessIdea.stage === 'validation' ? 2 : 3,
      has_team: true,
      has_funding: selectedBusinessIdea.stage !== 'idea',
      monthly_revenue: selectedBusinessIdea.stage === 'validation' ? 5000 : 0,
      burn_rate: 10000,
      runway_months: 12,
      customer_count: selectedBusinessIdea.stage === 'validation' ? 100 : 0,
      growth_rate: 0.1,
      team_size: 3,
      founder_experience: 2,
      market_size: 10000000,
      competition_level: 6,
      industry_category: 1,
      business_model: 1,
      target_market_size: 1000000,
      product_complexity: 0.6,
      monthly_revenue_per_customer: 50,
      monthly_churn_rate: 0.05,
      gross_margin: 0.7
    };

    switch (activeTab) {
      case 'success_prediction':
        return (
          <SuccessProbabilityEngine
            businessIdeaId={selectedBusinessIdea.id}
            businessIdeaData={selectedBusinessIdea}
            userId={user?.id}
          />
        );
      case 'risk_assessment':
        return (
          <RiskAssessmentDashboard
            businessIdeaId={selectedBusinessIdea.id}
            businessIdeaData={selectedBusinessIdea}
            userId={user?.id}
          />
        );
      case 'market_trends':
        return (
          <MarketTrendPrediction
            businessIdeaId={selectedBusinessIdea.id}
            businessIdeaData={selectedBusinessIdea}
            userId={user?.id}
          />
        );
      case 'optimization':
        return (
          <PerformanceOptimizationEngine
            businessIdeaId={selectedBusinessIdea.id}
            businessIdeaData={selectedBusinessIdea}
            userId={user?.id}
          />
        );
      case 'enhanced_analytics':
      case 'failure_prediction':
      case 'timing_optimization':
      case 'competitor_analysis':
      case 'cac_prediction':
        return (
          <EnhancedPredictiveAnalyticsDashboard
            businessData={businessData}
            className="border-0"
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              {t('ai.predictive.title', 'Predictive Intelligence Dashboard')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('ai.predictive.subtitle', 'AI-powered predictions and intelligence for business success')}
            </p>
          </div>

          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <ProactiveAINotifications userId={user?.id} />
            <div className={`flex items-center space-x-2 px-3 py-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Brain className="w-4 h-4 text-purple-500" />
              <span className="text-sm text-purple-700 dark:text-purple-300">
                {t('ai.predictive.active', 'Predictive AI Active')}
              </span>
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
            </div>
          </div>
        </div>

        {/* Business Idea Selection */}
        <div className="glass-light border border-glass-border rounded-lg p-4">
          <h3 className="font-semibold text-glass-primary mb-3">
            {t('ai.predictive.selectIdea', 'Select Business Idea for Analysis')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {businessIdeas.map((idea) => (
              <button
                key={idea.id}
                onClick={() => setSelectedBusinessIdea(idea)}
                className={`text-left p-3 rounded-lg border transition-all duration-200 ${
                  selectedBusinessIdea?.id === idea.id
                    ? 'border-purple-500 bg-purple-500/10 backdrop-blur-sm'
                    : 'border-glass-border hover:border-purple-300 glass-hover'
                }`}
              >
                <h4 className="font-medium text-glass-primary">{idea.title}</h4>
                <p className="text-sm text-glass-secondary mt-1">{idea.description}</p>
                <div className={`flex items-center space-x-2 mt-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-xs px-2 py-1 glass-light text-glass-secondary rounded-full">
                    {idea.industry}
                  </span>
                  <span className="text-xs px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full">
                    {idea.stage}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Predictive Metrics Overview */}
        {selectedBusinessIdea && metrics && (
          <div className="glass-morphism border border-purple-500/30 rounded-lg p-6 bg-gradient-to-r from-purple-500/10 to-blue-500/10">
            <div className={`flex items-center justify-between mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-lg font-semibold text-glass-primary">
                {t('ai.predictive.overview', 'Predictive Intelligence Overview')}
              </h3>
              <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  onClick={generatePredictiveMetrics}
                  disabled={isRefreshing}
                  className={`flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                  <span>{t('ai.predictive.refresh', 'Refresh')}</span>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="text-center p-4 glass-light rounded-lg">
                <TrendingUp className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <div className={`text-2xl font-bold ${getScoreColor(metrics.success_probability)}`}>
                  {metrics.success_probability}%
                </div>
                <div className="text-sm text-glass-secondary">
                  {t('ai.predictive.successProbability', 'Success Probability')}
                </div>
              </div>

              <div className="text-center p-4 glass-light rounded-lg">
                <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-2" />
                <div className={`text-2xl font-bold ${getScoreColor(metrics.risk_score, true)}`}>
                  {metrics.risk_score}%
                </div>
                <div className="text-sm text-glass-secondary">
                  {t('ai.predictive.riskScore', 'Risk Score')}
                </div>
              </div>

              <div className="text-center p-4 glass-light rounded-lg">
                <Target className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                <div className={`text-2xl font-bold ${getScoreColor(metrics.market_opportunity)}`}>
                  {metrics.market_opportunity}%
                </div>
                <div className="text-sm text-glass-secondary">
                  {t('ai.predictive.marketOpportunity', 'Market Opportunity')}
                </div>
              </div>

              <div className="text-center p-4 glass-light rounded-lg">
                <Zap className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                <div className={`text-2xl font-bold ${getScoreColor(metrics.optimization_potential)}`}>
                  {metrics.optimization_potential}%
                </div>
                <div className="text-sm text-glass-secondary">
                  {t('ai.predictive.optimizationPotential', 'Optimization Potential')}
                </div>
              </div>

              <div className="text-center p-4 glass-light rounded-lg">
                <Award className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                <div className={`text-2xl font-bold ${getScoreColor(metrics.confidence_level)}`}>
                  {metrics.confidence_level}%
                </div>
                <div className="text-sm text-glass-secondary">
                  {t('ai.predictive.confidenceLevel', 'Confidence Level')}
                </div>
              </div>
            </div>

            <div className="mt-4 text-center">
              <div className={`flex items-center justify-center space-x-2 text-sm text-glass-secondary ${isRTL ? "flex-row-reverse" : ""}`}>
                <Calendar className="w-4 h-4" />
                <span>
                  {t('ai.predictive.lastUpdated', 'Last updated')}: {metrics.last_updated.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Tabs */}
        {selectedBusinessIdea && (
          <div className={`flex space-x-2 overflow-x-auto pb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg whitespace-nowrap transition-colors ${
                    activeTab === tab.id
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                  }`}
                >
                  <IconComponent className="w-4 h-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        )}

        {/* Tab Content */}
        {selectedBusinessIdea && activeTab !== 'overview' && (
          <div className="min-h-96">
            {renderTabContent()}
          </div>
        )}

        {/* No Business Idea Selected */}
        {!selectedBusinessIdea && (
          <div className="text-center py-12">
            <Brain className="w-16 h-16 text-purple-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {t('ai.predictive.selectToStart', 'Select a Business Idea to Start')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {t('ai.predictive.selectDescription', 'Choose a business idea above to access predictive intelligence features')}
            </p>
          </div>
        )}
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};

export default PredictiveIntelligenceDashboard;
