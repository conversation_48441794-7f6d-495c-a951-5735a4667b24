import React, { useEffect, createContext, useContext } from 'react';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { selectActiveTab, setActiveTab } from '../../store/uiSlice';

interface TabsContextType {
  value: string;
  onValueChange: (value: string) => void;
  tabGroupId: string;
}

const TabsContext = createContext<TabsContextType | undefined>(undefined);

interface TabsProps {
  children: React.ReactNode;
  value?: string;
  onValueChange?: (value: string) => void;
  className?: string;
  tabGroupId: string; // Required for Redux state management
}

interface TabsListProps {
  children: React.ReactNode;
  className?: string;
}

interface TabsTriggerProps {
  children: React.ReactNode;
  value: string;
  className?: string;
  tabGroupId: string; // Required for Redux state management
}

interface TabsContentProps {
  children: React.ReactNode;
  value: string;
  className?: string;
  tabGroupId: string; // Required for Redux state management
}

export const Tabs: React.FC<TabsProps> = ({
  children,
  value: controlledValue,
  onValueChange,
  className = '',
  tabGroupId
}) => {
  const dispatch = useAppDispatch();
  const reduxValue = useAppSelector(selectActiveTab(tabGroupId));

  // Use controlled value if provided, otherwise use Redux state
  const currentValue = controlledValue || reduxValue;

  // Initialize Redux state if not set and controlled value is provided
  useEffect(() => {
    if (controlledValue && !reduxValue) {
      dispatch(setActiveTab({ tabGroupId, value: controlledValue }));
    }
  }, [dispatch, tabGroupId, controlledValue, reduxValue]);

  // Create context value for child components
  const contextValue = {
    value: currentValue || '',
    onValueChange: (newValue: string) => {
      // Update Redux state
      dispatch(setActiveTab({ tabGroupId, value: newValue }));
      // Call external onChange if provided
      onValueChange?.(newValue);
    },
    tabGroupId
  };

  return (
    <TabsContext.Provider value={contextValue}>
      <div className={className}>
        {children}
      </div>
    </TabsContext.Provider>
  );
};

export const TabsList: React.FC<TabsListProps> = ({ children, className = '' }) => {
  return (
    <div className={`inline-flex h-10 items-center justify-center rounded-md glass-light border border-glass-border p-1 text-glass-secondary ${className}`}>
      {children}
    </div>
  );
};

export const TabsTrigger: React.FC<TabsTriggerProps> = ({ children, value, className = '', tabGroupId }) => {
  const context = useContext(TabsContext);
  const dispatch = useAppDispatch();
  const reduxValue = useAppSelector(selectActiveTab(tabGroupId));

  // Use context if available, otherwise use Redux directly
  const selectedValue = context?.value || reduxValue || '';
  const handleValueChange = context?.onValueChange || ((newValue: string) => {
    dispatch(setActiveTab({ tabGroupId, value: newValue }));
  });

  const isSelected = selectedValue === value;

  return (
    <button
      className={`
        inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium
        transition-all focus-visible:outline-none focus-visible:ring-2
        focus-visible:ring-glass-border focus-visible:ring-offset-2 disabled:pointer-events-none
        disabled:opacity-50
        ${isSelected
          ? 'glass-morphism text-glass-primary shadow-glass border border-glass-border'
          : 'hover:bg-glass-hover text-glass-secondary hover:text-glass-primary'
        }
        ${className}
      `}
      onClick={() => handleValueChange(value)}
    >
      {children}
    </button>
  );
};

export const TabsContent: React.FC<TabsContentProps> = ({ children, value, className = '', tabGroupId }) => {
  const context = useContext(TabsContext);
  const reduxValue = useAppSelector(selectActiveTab(tabGroupId));

  // Use context if available, otherwise use Redux directly
  const selectedValue = context?.value || reduxValue || '';

  if (selectedValue !== value) {
    return null;
  }

  return (
    <div className={`mt-2 text-glass-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-glass-border focus-visible:ring-offset-2 ${className}`}>
      {children}
    </div>
  );
};
