/**
 * Unified Welcome Section
 * Consolidated welcome section that adapts to different user roles
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText } from '../../../common';
import { User } from '../../../../store/authSlice';
import { DashboardRole } from '../../../../types/dashboard';
// Dashboard theme functionality moved to Redux - using default styling for now
import { getAnalyticsOverview, getUserAnalytics } from '../../../../services/analyticsApi';
import { 
  ShieldAlert, 
  TrendingUp, 
  Users, 
  Activity, 
  Shield,
  Crown,
  UserCheck,
  TrendingDown,
  Briefcase,
  Eye
} from 'lucide-react';

interface UnifiedWelcomeSectionProps {
  user: User | null;
  role: DashboardRole;
  onUpdate?: (sectionId: string, data: any) => void;
  className?: string;
}

interface WelcomeStats {
  activeUsers: number;
  todayActivity: number;
  growthRate: number;
  systemStatus: 'online' | 'offline' | 'maintenance';
}

const UnifiedWelcomeSection: React.FC<UnifiedWelcomeSectionProps> = ({
  user,
  role,
  onUpdate,
  className = '',
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  // Default styling functions to replace removed dashboard theme
  const getCardClasses = () => 'glass-morphism border border-glass-border rounded-lg p-6';
  const getTextClasses = (type: 'primary' | 'secondary') =>
    type === 'primary' ? 'text-glass-primary' : 'text-glass-secondary';

  const [stats, setStats] = useState<WelcomeStats>({
    activeUsers: 0,
    todayActivity: 0,
    growthRate: 0,
    systemStatus: 'online'
  });
  const [loading, setLoading] = useState(true);

  // Fetch role-specific welcome data
  useEffect(() => {
    const fetchWelcomeData = async () => {
      try {
        setLoading(true);

        // Fetch real analytics data based on role
        let analyticsData;
        if (role === 'super_admin' || role === 'admin') {
          analyticsData = await getAnalyticsOverview('7d');
        } else if (user?.id) {
          analyticsData = await getUserAnalytics(user.id, '7d');
        }

        // Convert analytics data to welcome stats format
        const realStats = convertAnalyticsToWelcomeStats(analyticsData, role);
        setStats(realStats);

        // Notify parent of data update
        onUpdate?.('welcome', realStats);
      } catch (error) {
        console.error('Error fetching welcome data:', error);
        // Fallback to role-specific stats on error
        const fallbackStats = getRoleSpecificStats(role);
        setStats(fallbackStats);
        onUpdate?.('welcome', fallbackStats);
      } finally {
        setLoading(false);
      }
    };

    fetchWelcomeData();
  }, [role, onUpdate, user?.id]);

  // Convert analytics data to welcome stats format
  const convertAnalyticsToWelcomeStats = (analyticsData: any, userRole: DashboardRole): WelcomeStats => {
    const baseStats = {
      systemStatus: 'online' as const,
    };

    if (!analyticsData) {
      return getRoleSpecificStats(userRole);
    }

    // Extract relevant data from analytics response
    const overview = analyticsData.overview || analyticsData;

    return {
      ...baseStats,
      activeUsers: overview.total_users || overview.active_users || 0,
      todayActivity: overview.total_business_ideas || overview.todayActivity || 0,
      growthRate: overview.growth_rate || 0,
    };
  };

  // Get role-specific fallback stats (used when API fails)
  const getRoleSpecificStats = (userRole: DashboardRole): WelcomeStats => {
    const baseStats = {
      systemStatus: 'online' as const,
    };

    switch (userRole) {
      case 'super_admin':
        return {
          ...baseStats,
          activeUsers: 1247,
          todayActivity: 3456,
          growthRate: 12.5,
        };
      case 'admin':
        return {
          ...baseStats,
          activeUsers: 856,
          todayActivity: 2134,
          growthRate: 8.3,
        };
      case 'moderator':
        return {
          ...baseStats,
          activeUsers: 234,
          todayActivity: 567,
          growthRate: 5.2,
        };
      case 'mentor':
        return {
          ...baseStats,
          activeUsers: 45,
          todayActivity: 123,
          growthRate: 15.7,
        };
      case 'investor':
        return {
          ...baseStats,
          activeUsers: 78,
          todayActivity: 234,
          growthRate: 22.1,
        };
      default: // user
        return {
          ...baseStats,
          activeUsers: 12,
          todayActivity: 34,
          growthRate: 7.8,
        };
    }
  };

  // Get role-specific title and subtitle
  const getRoleContent = () => {
    const username = user?.username || t('common.user', 'User');
    
    switch (role) {
      case 'super_admin':
        return {
          title: t('dashboard.welcome.superAdmin', 'Welcome back, Super Admin {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.superAdmin', 'Complete system control and monitoring'),
          icon: Crown,
          iconColor: 'text-red-400',
          showSystemStatus: true,
          showSecurityNotice: true,
        };
      case 'admin':
        return {
          title: t('dashboard.welcome.admin', 'Welcome back, {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.admin', 'Platform administration and management'),
          icon: Shield,
          iconColor: 'text-purple-400',
          showSystemStatus: true,
          showSecurityNotice: true,
        };
      case 'moderator':
        return {
          title: t('dashboard.welcome.moderator', 'Welcome, Moderator {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.moderator', 'Monitor and manage community content'),
          icon: Eye,
          iconColor: 'text-blue-400',
          showSystemStatus: false,
          showSecurityNotice: false,
        };
      case 'mentor':
        return {
          title: t('dashboard.welcome.mentor', 'Welcome, {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.mentor', 'Guide and support your mentees'),
          icon: UserCheck,
          iconColor: 'text-blue-400',
          showSystemStatus: false,
          showSecurityNotice: false,
        };
      case 'investor':
        return {
          title: t('dashboard.welcome.investor', 'Welcome, {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.investor', 'Track investments and opportunities'),
          icon: Briefcase,
          iconColor: 'text-green-400',
          showSystemStatus: false,
          showSecurityNotice: false,
        };
      default: // user
        return {
          title: t('dashboard.welcome.user', 'Welcome, {{username}}!', { username }),
          subtitle: t('dashboard.subtitle.user', 'Your personal dashboard'),
          icon: Users,
          iconColor: 'text-indigo-400',
          showSystemStatus: false,
          showSecurityNotice: false,
        };
    }
  };

  const roleContent = getRoleContent();
  const IconComponent = roleContent.icon;

  const getSystemStatusColor = () => {
    switch (stats.systemStatus) {
      case 'online':
        return 'text-green-400 bg-green-400';
      case 'maintenance':
        return 'text-yellow-400 bg-yellow-400';
      case 'offline':
        return 'text-red-400 bg-red-400';
      default:
        return 'text-green-400 bg-green-400';
    }
  };

  const getSystemStatusText = () => {
    switch (stats.systemStatus) {
      case 'online':
        return t('dashboard.system.online', 'System Online');
      case 'maintenance':
        return t('dashboard.system.maintenance', 'Under Maintenance');
      case 'offline':
        return t('dashboard.system.offline', 'System Offline');
      default:
        return t('dashboard.system.online', 'System Online');
    }
  };

  return (
    <div className={`${getCardClasses()} ${className}`}>
      {/* Welcome Header */}
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`p-3 bg-black/20 rounded-lg ${isRTL ? 'ml-4' : 'mr-4'}`}>
            <IconComponent className={`w-8 h-8 ${roleContent.iconColor}`} />
          </div>
          <div>
            <RTLText as="h1" className={`text-3xl font-bold ${getTextClasses('primary')} mb-2`}>
              {roleContent.title}
            </RTLText>
            <RTLText className={`${getTextClasses('secondary')} text-lg`}>
              {roleContent.subtitle}
            </RTLText>
          </div>
        </div>
        
        {/* System Status (for admin roles) */}
        {roleContent.showSystemStatus && (
          <div className="hidden md:flex items-center space-x-4">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`w-3 h-3 rounded-full animate-pulse ${isRTL ? 'ml-2' : 'mr-2'} ${getSystemStatusColor().split(' ')[1]}`}></div>
              <span className={`text-sm font-medium ${getSystemStatusColor().split(' ')[0]}`}>
                {getSystemStatusText()}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Quick Stats Overview (for admin/moderator roles) */}
      {(role === 'admin' || role === 'super_admin' || role === 'moderator') && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="bg-black/20 rounded-xl p-4">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Users size={20} className="text-blue-400" />
              </div>
              <div className={isRTL ? 'mr-3' : 'ml-3'}>
                <RTLText as="p" className={`text-sm ${getTextClasses('secondary')}`}>
                  {t('dashboard.stats.activeUsers', 'Active Users')}
                </RTLText>
                <RTLText as="p" className={`text-xl font-bold ${getTextClasses('primary')}`}>
                  {loading ? (
                    <span className="animate-pulse">---</span>
                  ) : (
                    stats.activeUsers.toLocaleString()
                  )}
                </RTLText>
              </div>
            </div>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Activity size={20} className="text-purple-400" />
              </div>
              <div className={isRTL ? 'mr-3' : 'ml-3'}>
                <RTLText as="p" className={`text-sm ${getTextClasses('secondary')}`}>
                  {t('dashboard.stats.todayActivity', 'Today\'s Activity')}
                </RTLText>
                <RTLText as="p" className={`text-xl font-bold ${getTextClasses('primary')}`}>
                  {loading ? (
                    <span className="animate-pulse">---</span>
                  ) : (
                    stats.todayActivity.toLocaleString()
                  )}
                </RTLText>
              </div>
            </div>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-green-500/20 rounded-lg">
                <TrendingUp size={20} className="text-green-400" />
              </div>
              <div className={isRTL ? 'mr-3' : 'ml-3'}>
                <RTLText as="p" className={`text-sm ${getTextClasses('secondary')}`}>
                  {t('dashboard.stats.growthRate', 'Growth Rate')}
                </RTLText>
                <RTLText as="p" className={`text-xl font-bold ${getTextClasses('primary')}`}>
                  {loading ? (
                    <span className="animate-pulse">---</span>
                  ) : (
                    `${stats.growthRate > 0 ? '+' : ''}${stats.growthRate.toFixed(1)}%`
                  )}
                </RTLText>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Security Notice (for admin roles) */}
      {roleContent.showSecurityNotice && (
        <div className="mt-6 p-4 bg-gradient-to-r from-red-900/30 to-orange-900/30 border border-red-500/30 rounded-xl">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="p-2 bg-red-500/20 rounded-lg">
              <ShieldAlert size={20} className="text-red-500" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText as="h4" className="font-semibold text-red-300 mb-1">
                {t('dashboard.security.notice', 'Security Notice')}
              </RTLText>
              <RTLText as="p" className="text-sm text-red-200">
                {role === 'super_admin' 
                  ? t('dashboard.security.superAdminNote', 'You have complete system access. Handle with extreme care.')
                  : t('dashboard.security.adminNote', 'You are accessing the admin panel. Handle data with care.')
                }
              </RTLText>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UnifiedWelcomeSection;
