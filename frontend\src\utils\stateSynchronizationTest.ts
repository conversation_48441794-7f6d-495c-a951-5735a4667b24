/**
 * State Synchronization Test Suite
 * Comprehensive tests for enhanced state synchronization features
 */

import { store } from '../store';
import { clearAuth, setAuthState } from '../store/authSlice';
import { getAuthToken, getRefreshToken, clearAuthTokens } from '../services/api';
import { stateSynchronizer } from './stateSynchronizer';
import { tokenExpirationHandler } from './tokenExpirationHandler';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'skip';
  message: string;
  duration: number;
  details?: any;
}

class StateSynchronizationTest {
  private results: TestResult[] = [];

  /**
   * Run all state synchronization tests
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting state synchronization test suite...');
    
    this.results = [];
    
    // Test 1: Basic state synchronization
    await this.testBasicStateSynchronization();
    
    // Test 2: Token-Redux consistency
    await this.testTokenReduxConsistency();
    
    // Test 3: Error recovery
    await this.testErrorRecovery();
    
    // Test 4: Token expiration handling
    await this.testTokenExpirationHandling();
    
    // Test 5: State synchronizer performance
    await this.testSynchronizerPerformance();
    
    // Test 6: Concurrent synchronization
    await this.testConcurrentSynchronization();
    
    console.log('✅ State synchronization test suite completed');
    this.printResults();
    
    return this.results;
  }

  /**
   * Test basic state synchronization functionality
   */
  private async testBasicStateSynchronization(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('🔄 Testing basic state synchronization...');
      
      // Perform synchronization
      const report = await stateSynchronizer.synchronizeState();
      
      // Verify report structure
      if (!report || typeof report.isConsistent !== 'boolean') {
        throw new Error('Invalid synchronization report structure');
      }
      
      // Check that sync results exist
      if (!Array.isArray(report.syncResults) || report.syncResults.length === 0) {
        throw new Error('No synchronization results returned');
      }
      
      // Verify each sync result has required properties
      for (const result of report.syncResults) {
        if (!result.action || typeof result.success !== 'boolean' || !result.timestamp) {
          throw new Error(`Invalid sync result structure: ${JSON.stringify(result)}`);
        }
      }
      
      this.addResult('Basic State Synchronization', 'pass', 
        `Synchronization completed. Consistent: ${report.isConsistent}`, startTime, {
          isConsistent: report.isConsistent,
          resultCount: report.syncResults.length,
          issues: report.issues.length
        });
      
    } catch (error) {
      this.addResult('Basic State Synchronization', 'fail', 
        `Test failed: ${error}`, startTime);
    }
  }

  /**
   * Test token-Redux state consistency
   */
  private async testTokenReduxConsistency(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('🔄 Testing token-Redux consistency...');
      
      // Get initial state
      const initialState = store.getState().auth;
      const initialToken = getAuthToken();
      const initialRefreshToken = getRefreshToken();
      
      // Test scenario 1: Clear Redux but keep tokens (simulated inconsistency)
      store.dispatch(clearAuth());
      
      // If tokens exist, this should be detected as inconsistent
      if (initialToken && initialRefreshToken) {
        const report = await stateSynchronizer.synchronizeState();
        
        if (report.isConsistent) {
          throw new Error('Failed to detect token-Redux inconsistency');
        }
      }
      
      // Test scenario 2: Restore state and verify consistency
      if (initialState.isAuthenticated && initialState.user) {
        store.dispatch(setAuthState({
          user: initialState.user,
          isAuthenticated: initialState.isAuthenticated
        }));
      }
      
      const finalReport = await stateSynchronizer.synchronizeState();
      
      this.addResult('Token-Redux Consistency', 'pass', 
        'Consistency checks completed successfully', startTime, {
          initiallyAuthenticated: initialState.isAuthenticated,
          hadTokens: !!(initialToken && initialRefreshToken),
          finalConsistency: finalReport.isConsistent
        });
      
    } catch (error) {
      this.addResult('Token-Redux Consistency', 'fail', 
        `Test failed: ${error}`, startTime);
    }
  }

  /**
   * Test error recovery mechanisms
   */
  private async testErrorRecovery(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('🔄 Testing error recovery...');
      
      // Test recovery from invalid state
      const report = await stateSynchronizer.synchronizeState();
      
      // Check if any recovery actions were taken
      const recoveryActions = report.syncResults.filter(result => 
        result.action.includes('cleanup') || result.action.includes('sync')
      );
      
      this.addResult('Error Recovery', 'pass', 
        `Recovery mechanisms tested. Actions taken: ${recoveryActions.length}`, startTime, {
          totalResults: report.syncResults.length,
          recoveryActions: recoveryActions.length,
          isConsistent: report.isConsistent
        });
      
    } catch (error) {
      this.addResult('Error Recovery', 'fail', 
        `Test failed: ${error}`, startTime);
    }
  }

  /**
   * Test token expiration handling
   */
  private async testTokenExpirationHandling(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('⏰ Testing token expiration handling...');
      
      // Test token status retrieval
      const tokenStatus = tokenExpirationHandler.getTokenStatus();
      
      // Verify status structure
      if (typeof tokenStatus.hasToken !== 'boolean' || 
          typeof tokenStatus.isExpired !== 'boolean') {
        throw new Error('Invalid token status structure');
      }
      
      // Test configuration update
      const originalConfig = { ...tokenExpirationHandler['config'] };
      tokenExpirationHandler.updateConfig({
        checkInterval: 60000 // 1 minute
      });
      
      // Restore original config
      tokenExpirationHandler.updateConfig(originalConfig);
      
      this.addResult('Token Expiration Handling', 'pass', 
        'Token expiration handler tested successfully', startTime, {
          hasToken: tokenStatus.hasToken,
          isExpired: tokenStatus.isExpired,
          timeUntilExpiry: tokenStatus.timeUntilExpiry,
          warningLevel: tokenStatus.warningLevel
        });
      
    } catch (error) {
      this.addResult('Token Expiration Handling', 'fail', 
        `Test failed: ${error}`, startTime);
    }
  }

  /**
   * Test synchronizer performance
   */
  private async testSynchronizerPerformance(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('⚡ Testing synchronizer performance...');
      
      // Perform multiple synchronizations and measure time
      const iterations = 5;
      const times: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        const iterationStart = Date.now();
        await stateSynchronizer.synchronizeState();
        times.push(Date.now() - iterationStart);
      }
      
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      
      // Performance should be reasonable (under 1 second per sync)
      if (avgTime > 1000) {
        throw new Error(`Synchronization too slow: ${avgTime}ms average`);
      }
      
      this.addResult('Synchronizer Performance', 'pass', 
        `Performance test completed. Avg: ${avgTime.toFixed(2)}ms`, startTime, {
          iterations,
          averageTime: avgTime,
          maxTime,
          allTimes: times
        });
      
    } catch (error) {
      this.addResult('Synchronizer Performance', 'fail', 
        `Test failed: ${error}`, startTime);
    }
  }

  /**
   * Test concurrent synchronization handling
   */
  private async testConcurrentSynchronization(): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log('🔄 Testing concurrent synchronization...');
      
      // Start multiple synchronizations concurrently
      const promises = [
        stateSynchronizer.synchronizeState(),
        stateSynchronizer.synchronizeState(),
        stateSynchronizer.synchronizeState()
      ];
      
      const results = await Promise.all(promises);
      
      // All should complete successfully
      if (results.length !== 3) {
        throw new Error('Not all concurrent synchronizations completed');
      }
      
      // Check that debouncing worked (should get similar results)
      const consistencyResults = results.map(r => r.isConsistent);
      const allSame = consistencyResults.every(result => result === consistencyResults[0]);
      
      this.addResult('Concurrent Synchronization', 'pass', 
        `Concurrent sync handled correctly. Consistent results: ${allSame}`, startTime, {
          resultCount: results.length,
          consistencyResults,
          allConsistent: allSame
        });
      
    } catch (error) {
      this.addResult('Concurrent Synchronization', 'fail', 
        `Test failed: ${error}`, startTime);
    }
  }

  /**
   * Add test result
   */
  private addResult(name: string, status: 'pass' | 'fail' | 'skip', 
                   message: string, startTime: number, details?: any): void {
    this.results.push({
      name,
      status,
      message,
      duration: Date.now() - startTime,
      details
    });
  }

  /**
   * Print test results
   */
  private printResults(): void {
    console.log('\n📊 State Synchronization Test Results:');
    console.log('=' .repeat(60));
    
    const passed = this.results.filter(r => r.status === 'pass').length;
    const failed = this.results.filter(r => r.status === 'fail').length;
    const skipped = this.results.filter(r => r.status === 'skip').length;
    
    this.results.forEach(result => {
      const icon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⏭️';
      console.log(`${icon} ${result.name}: ${result.message} (${result.duration}ms)`);
      
      if (result.details && process.env.NODE_ENV === 'development') {
        console.log(`   Details:`, result.details);
      }
    });
    
    console.log('\n📈 Summary:');
    console.log(`   Passed: ${passed}`);
    console.log(`   Failed: ${failed}`);
    console.log(`   Skipped: ${skipped}`);
    console.log(`   Total: ${this.results.length}`);
    
    if (failed === 0) {
      console.log('🎉 All state synchronization tests passed!');
    } else {
      console.log('⚠️ Some state synchronization tests failed');
    }
  }

  /**
   * Get test results
   */
  getResults(): TestResult[] {
    return [...this.results];
  }
}

// Export singleton instance
export const stateSynchronizationTest = new StateSynchronizationTest();

// Export for console debugging
if (typeof window !== 'undefined') {
  (window as any).stateSynchronizationTest = stateSynchronizationTest;
  console.log('🧪 State synchronization test loaded. Use stateSynchronizationTest.runAllTests() in console.');
}

export default stateSynchronizationTest;
