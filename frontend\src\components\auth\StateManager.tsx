/**
 * State Manager Component
 * Initializes and manages state synchronization and token expiration monitoring
 */

import React, { useEffect, useRef } from 'react';
import { useAppSelector } from '../../store/hooks';
import { stateSynchronizer } from '../../utils/stateSynchronizer';
import { tokenExpirationHandler, ExpirationWarning } from '../../utils/tokenExpirationHandler';
import { useToast } from '../../hooks/useToast';

interface StateManagerProps {
  children?: React.ReactNode;
}

const StateManager: React.FC<StateManagerProps> = ({ children }) => {
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  const { showToast } = useToast();
  const initializationRef = useRef(false);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  /**
   * Initialize state management systems
   */
  useEffect(() => {
    if (initializationRef.current) return;
    
    console.log('🔧 Initializing state management systems...');
    
    // Initialize state synchronization
    initializeStateSynchronization();
    
    // Initialize token expiration monitoring if authenticated
    if (isAuthenticated) {
      initializeTokenMonitoring();
    }
    
    initializationRef.current = true;
    
    return () => {
      cleanup();
    };
  }, []);

  /**
   * Handle authentication state changes
   */
  useEffect(() => {
    if (isAuthenticated && user) {
      // User logged in - start token monitoring
      initializeTokenMonitoring();
    } else {
      // User logged out - stop token monitoring
      stopTokenMonitoring();
    }
  }, [isAuthenticated, user]);

  /**
   * Initialize state synchronization
   */
  const initializeStateSynchronization = async (): Promise<void> => {
    try {
      console.log('🔄 Starting state synchronization...');
      
      // Perform initial synchronization
      const report = await stateSynchronizer.synchronizeState();
      
      if (!report.isConsistent) {
        console.warn('⚠️ State inconsistencies detected:', report.issues);
        
        // Show warning to user in development
        if (process.env.NODE_ENV === 'development') {
          showToast({
            type: 'warning',
            title: 'State Sync Warning',
            message: `${report.issues.length} state inconsistencies detected`,
            duration: 5000
          });
        }
      } else {
        console.log('✅ State synchronization completed successfully');
      }
      
    } catch (error) {
      console.error('❌ State synchronization initialization failed:', error);
      
      if (process.env.NODE_ENV === 'development') {
        showToast({
          type: 'error',
          title: 'State Sync Error',
          message: 'Failed to initialize state synchronization',
          duration: 5000
        });
      }
    }
  };

  /**
   * Initialize token expiration monitoring
   */
  const initializeTokenMonitoring = (): void => {
    try {
      console.log('⏰ Starting token expiration monitoring...');
      
      // Stop any existing monitoring
      stopTokenMonitoring();
      
      // Register expiration warning callback
      unsubscribeRef.current = tokenExpirationHandler.onExpirationWarning(handleExpirationWarning);
      
      // Start monitoring
      tokenExpirationHandler.startMonitoring();
      
      console.log('✅ Token expiration monitoring started');
      
    } catch (error) {
      console.error('❌ Token monitoring initialization failed:', error);
    }
  };

  /**
   * Stop token expiration monitoring
   */
  const stopTokenMonitoring = (): void => {
    try {
      // Unsubscribe from warnings
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
      
      // Stop monitoring
      tokenExpirationHandler.stopMonitoring();
      
      console.log('🛑 Token expiration monitoring stopped');
      
    } catch (error) {
      console.error('❌ Error stopping token monitoring:', error);
    }
  };

  /**
   * Handle token expiration warnings
   */
  const handleExpirationWarning = (warning: ExpirationWarning): void => {
    console.log(`⚠️ Token expiration warning (${warning.warningLevel}):`, warning.message);
    
    // Show appropriate toast based on warning level
    switch (warning.warningLevel) {
      case 'critical':
        showToast({
          type: 'error',
          title: 'Session Expiring Soon',
          message: warning.message,
          duration: 10000,
          persistent: true
        });
        break;
        
      case 'warning':
        showToast({
          type: 'warning',
          title: 'Session Warning',
          message: warning.message,
          duration: 8000
        });
        break;
        
      case 'info':
        // Only show info warnings in development
        if (process.env.NODE_ENV === 'development') {
          showToast({
            type: 'info',
            title: 'Session Info',
            message: warning.message,
            duration: 5000
          });
        }
        break;
    }
    
    // Handle logout action
    if (warning.action === 'logout') {
      showToast({
        type: 'error',
        title: 'Session Expired',
        message: 'Your session has expired. Please log in again.',
        duration: 10000,
        persistent: true
      });
    }
  };

  /**
   * Cleanup function
   */
  const cleanup = (): void => {
    try {
      console.log('🧹 Cleaning up state management systems...');
      
      // Stop token monitoring
      stopTokenMonitoring();
      
      // Reset token expiration handler
      tokenExpirationHandler.reset();
      
      console.log('✅ State management cleanup completed');
      
    } catch (error) {
      console.error('❌ Error during state management cleanup:', error);
    }
  };

  // Periodic state synchronization for authenticated users
  useEffect(() => {
    if (!isAuthenticated) return;
    
    const syncInterval = setInterval(async () => {
      try {
        const report = await stateSynchronizer.synchronizeState();
        
        if (!report.isConsistent && process.env.NODE_ENV === 'development') {
          console.warn('⚠️ Periodic sync detected inconsistencies:', report.issues);
        }
      } catch (error) {
        console.error('❌ Periodic state sync failed:', error);
      }
    }, 5 * 60 * 1000); // Every 5 minutes
    
    return () => clearInterval(syncInterval);
  }, [isAuthenticated]);

  // Development-only: Expose state management tools to console
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      (window as any).stateManager = {
        synchronizer: stateSynchronizer,
        tokenHandler: tokenExpirationHandler,
        forceSync: () => stateSynchronizer.forceSynchronization(),
        getTokenStatus: () => tokenExpirationHandler.getTokenStatus(),
        getSyncHistory: () => stateSynchronizer.getSyncHistory()
      };
      
      console.log('🔧 State manager tools loaded. Use stateManager in console.');
    }
  }, []);

  // This component doesn't render anything visible
  return children ? <>{children}</> : null;
};

export default StateManager;
