import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User, authAPI, getAuthToken, getRefreshToken, clearAuthTokens } from '../services/api';

// Define the state interface
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Helper function to check if we have valid tokens on startup
const hasValidTokensOnStartup = (): boolean => {
  try {
    const token = getAuthToken();
    const refreshToken = getRefreshToken();

    if (!token || !refreshToken) {
      return false;
    }

    // Check if token is not expired (basic client-side check)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const now = Math.floor(Date.now() / 1000);
    const isExpired = payload.exp < now;

    return !isExpired;
  } catch (error) {
    console.warn('Error checking token validity on startup:', error);
    return false;
  }
};

// Initial state with smart token-based authentication detection
const initialState: AuthState = {
  user: null,
  isAuthenticated: hasValidTokensOnStartup(), // Set based on token presence and validity
  isLoading: false,
  error: null,
};

// Async thunks

// Initialize authentication state on app startup
export const initializeAuth = createAsyncThunk(
  'auth/initializeAuth',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      console.log('🔄 Initializing authentication state...');

      const token = getAuthToken();
      const refreshToken = getRefreshToken();

      if (!token || !refreshToken) {
        console.log('❌ No tokens found, clearing auth state');
        dispatch(authSlice.actions.clearAuth());
        return rejectWithValue('No authentication tokens');
      }

      // Check token expiration
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const now = Math.floor(Date.now() / 1000);
        const isExpired = payload.exp < now;

        if (isExpired) {
          console.log('⏰ Token expired, attempting refresh...');
          const refreshed = await authAPI.refreshToken();
          if (!refreshed) {
            console.log('❌ Token refresh failed, clearing auth state');
            // Clear auth state will be handled by the rejected case
            return rejectWithValue('Token refresh failed');
          }
          console.log('✅ Token refreshed successfully');
        }
      } catch (tokenError) {
        console.warn('⚠️ Invalid token format, attempting to get current user anyway');
      }

      // Fetch current user to validate authentication
      const user = await authAPI.getCurrentUser();
      console.log('✅ Authentication initialized successfully:', user.username);

      return user;
    } catch (error: any) {
      console.log('❌ Authentication initialization failed:', error.message);
      // Clear auth state will be handled by the rejected case
      return rejectWithValue(error.message || 'Authentication initialization failed');
    }
  }
);

export const login = createAsyncThunk(
  'auth/login',
  async ({ username, password }: { username: string; password: string }, { rejectWithValue }) => {
    try {
      console.log('🔐 Starting login process for:', username);
      const user = await authAPI.login(username, password);

      // Verify tokens were stored
      const token = getAuthToken();
      const refreshToken = getRefreshToken();

      if (!token || !refreshToken) {
        console.error('❌ Login succeeded but tokens not stored properly');
        return rejectWithValue('Authentication tokens not stored properly');
      }

      console.log('✅ Login successful, tokens stored:', {
        hasToken: !!token,
        hasRefreshToken: !!refreshToken,
        user: user.username
      });

      return user;
    } catch (err: any) {
      console.error('❌ Login failed:', err);

      // Extract meaningful error message
      let errorMessage = 'Login failed';
      if (err?.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err?.message) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      console.error('Login error message:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authAPI.logout();
      return null;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Logout failed');
    }
  }
);

export const register = createAsyncThunk(
  'auth/register',
  async (userData: {
    username: string;
    email: string;
    password: string;
    password_confirm: string;
    first_name?: string;
    last_name?: string;
  }, { rejectWithValue }) => {
    try {
      console.log('Registering user with data:', {
        ...userData,
        password: '***',
        password_confirm: '***'
      });

      const user = await authAPI.register(userData);
      console.log('Registration successful:', user);
      return user;
    } catch (err: any) {
      console.error('Registration error details:', err);

      // Extract meaningful error message
      let errorMessage = 'Registration failed';
      if (err?.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err?.response?.data?.username) {
        errorMessage = `Username: ${err.response.data.username[0]}`;
      } else if (err?.response?.data?.email) {
        errorMessage = `Email: ${err.response.data.email[0]}`;
      } else if (err?.response?.data?.password) {
        errorMessage = `Password: ${err.response.data.password[0]}`;
      } else if (err?.message) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      console.error('Registration error message:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      // Check if we have a token first using the correct token key
      const token = getAuthToken();
      if (!token) {
        if (process.env.NODE_ENV === 'development') {
          console.log('No access token found');
        }
        // Clear auth state if no token
        dispatch(authSlice.actions.clearAuth());
        return rejectWithValue('No authentication token');
      }

      const user = await authAPI.getCurrentUser();
      if (process.env.NODE_ENV === 'development') {
        console.log('Current user fetched successfully:', user);
      }
      return user;
    } catch (err: any) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching current user:', err);
      }

      // Handle authentication errors gracefully
      if (err?.status === 401) {
        clearAuthTokens();
        // Clear Redux auth state on 401
        dispatch(authSlice.actions.clearAuth());
      }

      return rejectWithValue(err?.message || 'Authentication failed');
    }
  }
);

// Create the slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearAuth: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.error = null;
      state.isLoading = false;

      // Allow token clearing for legitimate logout
      if (typeof window !== 'undefined' && (window as any).tokenProtection) {
        (window as any).tokenProtection.allowClearing();
        (window as any).tokenProtection.clearBackup();
      }

      clearAuthTokens();
    },
    setAuthState: (state, action: PayloadAction<{ user: User; isAuthenticated: boolean }>) => {
      state.user = action.payload.user;
      state.isAuthenticated = action.payload.isAuthenticated;
      state.error = null;
      state.isLoading = false;
    },
    syncAuthWithTokens: (state) => {
      // Synchronize auth state with current token presence
      const hasValidTokens = hasValidTokensOnStartup();
      if (hasValidTokens && !state.isAuthenticated) {
        // Tokens exist but state shows not authenticated - this shouldn't happen with proper initialization
        console.warn('⚠️ Auth state out of sync: tokens exist but isAuthenticated is false');
        state.isAuthenticated = true;
        state.error = null; // Clear any previous errors
      } else if (!hasValidTokens && state.isAuthenticated) {
        // No valid tokens but state shows authenticated - clear the state
        console.warn('⚠️ Auth state out of sync: no valid tokens but isAuthenticated is true');
        state.user = null;
        state.isAuthenticated = false;
        state.error = 'Authentication expired';
      }
    },
    forceAuthStateUpdate: (state, action: PayloadAction<{ isAuthenticated: boolean; user?: User | null; error?: string | null }>) => {
      // Force update auth state - used by state synchronizer
      state.isAuthenticated = action.payload.isAuthenticated;
      if (action.payload.user !== undefined) {
        state.user = action.payload.user;
      }
      if (action.payload.error !== undefined) {
        state.error = action.payload.error;
      }
      state.isLoading = false;
    },
  },
  extraReducers: (builder) => {
    // Login
    builder.addCase(login.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(login.fulfilled, (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.user = action.payload;
      state.isAuthenticated = true;
      state.error = null;
    });
    builder.addCase(login.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Logout
    builder.addCase(logout.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(logout.fulfilled, (state) => {
      state.isLoading = false;
      state.user = null;
      state.isAuthenticated = false;
      state.error = null;
    });
    builder.addCase(logout.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Register
    builder.addCase(register.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(register.fulfilled, (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.user = action.payload;
      state.isAuthenticated = true;
      state.error = null;
    });
    builder.addCase(register.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Get current user
    builder.addCase(getCurrentUser.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(getCurrentUser.fulfilled, (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.user = action.payload;
      state.isAuthenticated = true;
      state.error = null;
    });
    builder.addCase(getCurrentUser.rejected, (state) => {
      state.isLoading = false;
      state.user = null;
      state.isAuthenticated = false;
    });

    // Initialize auth cases
    builder.addCase(initializeAuth.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(initializeAuth.fulfilled, (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.user = action.payload;
      state.isAuthenticated = true;
      state.error = null;
    });
    builder.addCase(initializeAuth.rejected, (state, action) => {
      state.isLoading = false;
      state.user = null;
      state.isAuthenticated = false;
      state.error = action.payload as string;
    });
  },
});

// Export actions and reducer
export const { clearError, clearAuth, setAuthState, syncAuthWithTokens, forceAuthStateUpdate } = authSlice.actions;
export default authSlice.reducer;
