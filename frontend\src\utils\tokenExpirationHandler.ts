/**
 * Token Expiration Handler
 * Provides consistent token expiration handling across the application
 */

import { store } from '../store';
import { clearAuth } from '../store/authSlice';
import { getAuthToken, getRefreshToken, getTokenExpiry, clearAuthTokens, authAPI } from '../services/api';
import { stateSynchronizer } from './stateSynchronizer';

interface ExpirationWarning {
  timeUntilExpiry: number;
  warningLevel: 'info' | 'warning' | 'critical';
  message: string;
  action: 'none' | 'refresh' | 'logout';
}

interface ExpirationHandlerConfig {
  warningThresholds: {
    info: number;      // 15 minutes
    warning: number;   // 5 minutes  
    critical: number;  // 1 minute
  };
  autoRefreshThreshold: number; // 2 minutes
  checkInterval: number;        // 30 seconds
  maxRefreshAttempts: number;   // 3 attempts
}

class TokenExpirationHandler {
  private config: ExpirationHandlerConfig = {
    warningThresholds: {
      info: 15 * 60 * 1000,      // 15 minutes
      warning: 5 * 60 * 1000,    // 5 minutes
      critical: 1 * 60 * 1000,   // 1 minute
    },
    autoRefreshThreshold: 2 * 60 * 1000, // 2 minutes
    checkInterval: 30 * 1000,             // 30 seconds
    maxRefreshAttempts: 3,
  };

  private intervalId: NodeJS.Timeout | null = null;
  private refreshAttempts = 0;
  private lastWarningLevel: string | null = null;
  private isRefreshing = false;
  private warningCallbacks: ((warning: ExpirationWarning) => void)[] = [];

  /**
   * Start monitoring token expiration
   */
  startMonitoring(): void {
    if (this.intervalId) {
      console.log('⚠️ Token expiration monitoring already started');
      return;
    }

    console.log('🕐 Starting token expiration monitoring');
    
    this.intervalId = setInterval(() => {
      this.checkTokenExpiration();
    }, this.config.checkInterval);

    // Initial check
    this.checkTokenExpiration();
  }

  /**
   * Stop monitoring token expiration
   */
  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('🛑 Token expiration monitoring stopped');
    }
  }

  /**
   * Check current token expiration status
   */
  private async checkTokenExpiration(): Promise<void> {
    try {
      const token = getAuthToken();
      const refreshToken = getRefreshToken();

      if (!token || !refreshToken) {
        // No tokens, stop monitoring
        this.stopMonitoring();
        return;
      }

      const expiry = getTokenExpiry();
      if (!expiry) {
        console.warn('⚠️ Could not determine token expiry');
        return;
      }

      const now = Date.now();
      const timeUntilExpiry = expiry - now;

      // Token already expired
      if (timeUntilExpiry <= 0) {
        console.log('⏰ Token has expired');
        await this.handleExpiredToken();
        return;
      }

      // Check if auto-refresh is needed
      if (timeUntilExpiry <= this.config.autoRefreshThreshold && !this.isRefreshing) {
        console.log('🔄 Token expiring soon, attempting auto-refresh');
        await this.attemptTokenRefresh();
        return;
      }

      // Check for warnings
      const warning = this.generateExpirationWarning(timeUntilExpiry);
      if (warning) {
        this.handleExpirationWarning(warning);
      }

    } catch (error) {
      console.error('❌ Error checking token expiration:', error);
    }
  }

  /**
   * Generate expiration warning based on time until expiry
   */
  private generateExpirationWarning(timeUntilExpiry: number): ExpirationWarning | null {
    let warningLevel: 'info' | 'warning' | 'critical' | null = null;
    
    if (timeUntilExpiry <= this.config.warningThresholds.critical) {
      warningLevel = 'critical';
    } else if (timeUntilExpiry <= this.config.warningThresholds.warning) {
      warningLevel = 'warning';
    } else if (timeUntilExpiry <= this.config.warningThresholds.info) {
      warningLevel = 'info';
    }

    // Only generate warning if level changed or is critical
    if (!warningLevel || (warningLevel === this.lastWarningLevel && warningLevel !== 'critical')) {
      return null;
    }

    this.lastWarningLevel = warningLevel;

    const minutes = Math.floor(timeUntilExpiry / (60 * 1000));
    const seconds = Math.floor((timeUntilExpiry % (60 * 1000)) / 1000);

    let message: string;
    let action: 'none' | 'refresh' | 'logout';

    switch (warningLevel) {
      case 'critical':
        message = `Session expires in ${seconds} seconds. Please save your work.`;
        action = 'refresh';
        break;
      case 'warning':
        message = `Session expires in ${minutes} minutes. Consider saving your work.`;
        action = 'refresh';
        break;
      case 'info':
        message = `Session expires in ${minutes} minutes.`;
        action = 'none';
        break;
    }

    return {
      timeUntilExpiry,
      warningLevel,
      message,
      action
    };
  }

  /**
   * Handle expiration warning
   */
  private handleExpirationWarning(warning: ExpirationWarning): void {
    console.log(`⚠️ Token expiration warning (${warning.warningLevel}):`, warning.message);

    // Notify registered callbacks
    this.warningCallbacks.forEach(callback => {
      try {
        callback(warning);
      } catch (error) {
        console.error('Error in expiration warning callback:', error);
      }
    });

    // Auto-refresh for critical warnings
    if (warning.warningLevel === 'critical' && warning.action === 'refresh') {
      this.attemptTokenRefresh();
    }
  }

  /**
   * Handle expired token
   */
  private async handleExpiredToken(): Promise<void> {
    console.log('⏰ Handling expired token');

    // Try to refresh first
    const refreshSuccess = await this.attemptTokenRefresh();
    
    if (!refreshSuccess) {
      // Refresh failed, clear auth state
      console.log('❌ Token refresh failed, clearing auth state');
      store.dispatch(clearAuth());
      clearAuthTokens();
      
      // Sync state
      await stateSynchronizer.forceSynchronization();
      
      // Stop monitoring
      this.stopMonitoring();
      
      // Notify callbacks about logout
      const logoutWarning: ExpirationWarning = {
        timeUntilExpiry: 0,
        warningLevel: 'critical',
        message: 'Session expired. Please log in again.',
        action: 'logout'
      };
      
      this.warningCallbacks.forEach(callback => {
        try {
          callback(logoutWarning);
        } catch (error) {
          console.error('Error in logout warning callback:', error);
        }
      });
    }
  }

  /**
   * Attempt to refresh the token
   */
  private async attemptTokenRefresh(): Promise<boolean> {
    if (this.isRefreshing) {
      console.log('🔄 Token refresh already in progress');
      return false;
    }

    if (this.refreshAttempts >= this.config.maxRefreshAttempts) {
      console.log('❌ Max refresh attempts reached');
      return false;
    }

    this.isRefreshing = true;
    this.refreshAttempts++;

    try {
      console.log(`🔄 Attempting token refresh (${this.refreshAttempts}/${this.config.maxRefreshAttempts})`);
      
      const success = await authAPI.refreshToken();
      
      if (success) {
        console.log('✅ Token refresh successful');
        this.refreshAttempts = 0; // Reset attempts on success
        this.lastWarningLevel = null; // Reset warning level
        
        // Sync state after successful refresh
        await stateSynchronizer.forceSynchronization();
        
        return true;
      } else {
        console.log('❌ Token refresh failed');
        return false;
      }
      
    } catch (error) {
      console.error('❌ Token refresh error:', error);
      return false;
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Register callback for expiration warnings
   */
  onExpirationWarning(callback: (warning: ExpirationWarning) => void): () => void {
    this.warningCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.warningCallbacks.indexOf(callback);
      if (index > -1) {
        this.warningCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Get current token status
   */
  getTokenStatus(): {
    hasToken: boolean;
    isExpired: boolean;
    timeUntilExpiry: number | null;
    warningLevel: 'info' | 'warning' | 'critical' | null;
  } {
    const token = getAuthToken();
    const expiry = getTokenExpiry();
    
    if (!token || !expiry) {
      return {
        hasToken: !!token,
        isExpired: false,
        timeUntilExpiry: null,
        warningLevel: null
      };
    }

    const now = Date.now();
    const timeUntilExpiry = expiry - now;
    const isExpired = timeUntilExpiry <= 0;

    let warningLevel: 'info' | 'warning' | 'critical' | null = null;
    if (!isExpired) {
      if (timeUntilExpiry <= this.config.warningThresholds.critical) {
        warningLevel = 'critical';
      } else if (timeUntilExpiry <= this.config.warningThresholds.warning) {
        warningLevel = 'warning';
      } else if (timeUntilExpiry <= this.config.warningThresholds.info) {
        warningLevel = 'info';
      }
    }

    return {
      hasToken: true,
      isExpired,
      timeUntilExpiry: isExpired ? 0 : timeUntilExpiry,
      warningLevel
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ExpirationHandlerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Token expiration handler config updated:', this.config);
  }

  /**
   * Reset handler state
   */
  reset(): void {
    this.stopMonitoring();
    this.refreshAttempts = 0;
    this.lastWarningLevel = null;
    this.isRefreshing = false;
    this.warningCallbacks = [];
  }
}

// Export singleton instance
export const tokenExpirationHandler = new TokenExpirationHandler();

// Export types
export type { ExpirationWarning, ExpirationHandlerConfig };

// Export for console debugging
if (typeof window !== 'undefined') {
  (window as any).tokenExpirationHandler = tokenExpirationHandler;
  console.log('⏰ Token expiration handler loaded. Use tokenExpirationHandler in console.');
}

export default tokenExpirationHandler;
