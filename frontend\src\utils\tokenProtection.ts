/**
 * Token Protection System
 * Prevents accidental token clearing and provides persistence protection
 */

import { getAuthToken, getRefreshToken } from '../services/api';

interface TokenState {
  accessToken: string | null;
  refreshToken: string | null;
  timestamp: number;
}

class TokenProtectionService {
  private static instance: TokenProtectionService;
  private tokenBackup: TokenState | null = null;
  private protectionEnabled = true;
  private clearingAllowed = false;

  static getInstance(): TokenProtectionService {
    if (!TokenProtectionService.instance) {
      TokenProtectionService.instance = new TokenProtectionService();
    }
    return TokenProtectionService.instance;
  }

  /**
   * Initialize token protection
   */
  initialize(): void {
    this.backupCurrentTokens();
    this.setupStorageMonitoring();
    console.log('🛡️ Token protection initialized');
  }

  /**
   * Backup current tokens
   */
  private backupCurrentTokens(): void {
    const accessToken = getAuthToken();
    const refreshToken = getRefreshToken();
    
    if (accessToken && refreshToken) {
      this.tokenBackup = {
        accessToken,
        refreshToken,
        timestamp: Date.now()
      };
      console.log('💾 Tokens backed up for protection');
    }
  }

  /**
   * Setup localStorage monitoring to detect token clearing
   */
  private setupStorageMonitoring(): void {
    // Monitor localStorage changes
    const originalSetItem = localStorage.setItem;
    const originalRemoveItem = localStorage.removeItem;
    const originalClear = localStorage.clear;

    localStorage.setItem = (key: string, value: string) => {
      if (key === 'yasmeen_auth_token' || key === 'yasmeen_refresh_token') {
        console.log('🔄 Token storage change detected:', {
          key,
          action: 'set',
          hasValue: !!value,
          valueLength: value?.length || 0
        });
        
        // Update backup when tokens are set
        if (value) {
          this.backupCurrentTokens();
        }
      }
      return originalSetItem.call(localStorage, key, value);
    };

    localStorage.removeItem = (key: string) => {
      if (key === 'yasmeen_auth_token' || key === 'yasmeen_refresh_token') {
        console.log('🗑️ Token removal detected:', {
          key,
          protectionEnabled: this.protectionEnabled,
          clearingAllowed: this.clearingAllowed,
          stack: new Error().stack?.split('\n').slice(1, 3).join('\n')
        });

        // If protection is enabled and clearing is not explicitly allowed, restore tokens
        if (this.protectionEnabled && !this.clearingAllowed && this.tokenBackup) {
          console.log('🛡️ Preventing unauthorized token clearing - restoring tokens');
          setTimeout(() => {
            this.restoreTokens();
          }, 100);
        }
      }
      return originalRemoveItem.call(localStorage, key);
    };

    localStorage.clear = () => {
      console.log('🗑️ localStorage.clear() called - checking token protection');
      
      if (this.protectionEnabled && !this.clearingAllowed && this.tokenBackup) {
        console.log('🛡️ Preventing localStorage.clear() from removing tokens');
        // Call original clear but restore tokens afterward
        const result = originalClear.call(localStorage);
        setTimeout(() => {
          this.restoreTokens();
        }, 100);
        return result;
      }
      
      return originalClear.call(localStorage);
    };
  }

  /**
   * Restore tokens from backup
   */
  private restoreTokens(): void {
    if (!this.tokenBackup) {
      console.log('⚠️ No token backup available for restoration');
      return;
    }

    const { accessToken, refreshToken, timestamp } = this.tokenBackup;
    const age = Date.now() - timestamp;
    
    // Only restore if backup is less than 5 minutes old
    if (age > 5 * 60 * 1000) {
      console.log('⚠️ Token backup too old, not restoring');
      return;
    }

    console.log('🔄 Restoring tokens from backup');
    localStorage.setItem('yasmeen_auth_token', accessToken);
    localStorage.setItem('yasmeen_refresh_token', refreshToken);
    
    console.log('✅ Tokens restored successfully');
  }

  /**
   * Allow token clearing (for legitimate logout)
   */
  allowClearing(): void {
    console.log('🔓 Token clearing temporarily allowed');
    this.clearingAllowed = true;
    
    // Reset after 5 seconds
    setTimeout(() => {
      this.clearingAllowed = false;
      console.log('🔒 Token clearing protection re-enabled');
    }, 5000);
  }

  /**
   * Disable protection (for testing)
   */
  disableProtection(): void {
    console.log('⚠️ Token protection disabled');
    this.protectionEnabled = false;
  }

  /**
   * Enable protection
   */
  enableProtection(): void {
    console.log('🛡️ Token protection enabled');
    this.protectionEnabled = true;
  }

  /**
   * Get current protection status
   */
  getStatus(): { enabled: boolean; hasBackup: boolean; backupAge?: number } {
    return {
      enabled: this.protectionEnabled,
      hasBackup: !!this.tokenBackup,
      backupAge: this.tokenBackup ? Date.now() - this.tokenBackup.timestamp : undefined
    };
  }

  /**
   * Force backup current tokens
   */
  forceBackup(): void {
    this.backupCurrentTokens();
  }

  /**
   * Clear backup (for logout)
   */
  clearBackup(): void {
    console.log('🗑️ Clearing token backup');
    this.tokenBackup = null;
  }
}

// Export singleton instance
export const tokenProtection = TokenProtectionService.getInstance();

// Auto-initialize in development
if (process.env.NODE_ENV === 'development') {
  tokenProtection.initialize();
  
  // Add to window for debugging
  (window as any).tokenProtection = tokenProtection;
  console.log('🛡️ Token protection loaded. Use tokenProtection in console for debugging.');
}

export default TokenProtectionService;
