/**
 * API Call Performance Tracker
 * Monitors and tracks authentication-related API calls for optimization
 */

interface ApiCallMetrics {
  endpoint: string;
  method: string;
  duration: number;
  status: number;
  timestamp: number;
  isAuthRelated: boolean;
  tokenUsed: boolean;
}

interface ApiPerformanceStats {
  totalCalls: number;
  authCalls: number;
  averageResponseTime: number;
  slowestCall: ApiCallMetrics | null;
  fastestCall: ApiCallMetrics | null;
  errorRate: number;
  tokenUsageRate: number;
  callsPerMinute: number;
  lastResetTime: number;
}

class ApiCallTracker {
  private calls: ApiCallMetrics[] = [];
  private maxStoredCalls = 1000; // Keep last 1000 calls
  private startTime = Date.now();

  /**
   * Track an API call
   */
  trackCall(
    endpoint: string,
    method: string,
    duration: number,
    status: number,
    tokenUsed: boolean = false
  ): void {
    const call: ApiCallMetrics = {
      endpoint,
      method,
      duration,
      status,
      timestamp: Date.now(),
      isAuthRelated: this.isAuthEndpoint(endpoint),
      tokenUsed,
    };

    this.calls.push(call);

    // Keep only recent calls
    if (this.calls.length > this.maxStoredCalls) {
      this.calls = this.calls.slice(-this.maxStoredCalls);
    }

    // Log slow auth-related calls
    if (call.isAuthRelated && duration > 1000) {
      console.warn(`🐌 Slow auth API call: ${method} ${endpoint} took ${duration}ms`);
    }
  }

  /**
   * Check if endpoint is authentication-related
   */
  private isAuthEndpoint(endpoint: string): boolean {
    const authEndpoints = [
      '/users/token/',
      '/users/token/refresh/',
      '/users/token/verify/',
      '/users/current/',
      '/users/profiles/my_profile/',
      '/auth/',
    ];

    return authEndpoints.some(authEndpoint => endpoint.includes(authEndpoint));
  }

  /**
   * Get performance statistics
   */
  getStats(): ApiPerformanceStats {
    if (this.calls.length === 0) {
      return {
        totalCalls: 0,
        authCalls: 0,
        averageResponseTime: 0,
        slowestCall: null,
        fastestCall: null,
        errorRate: 0,
        tokenUsageRate: 0,
        callsPerMinute: 0,
        lastResetTime: this.startTime,
      };
    }

    const authCalls = this.calls.filter(call => call.isAuthRelated);
    const errorCalls = this.calls.filter(call => call.status >= 400);
    const tokenCalls = this.calls.filter(call => call.tokenUsed);

    const totalDuration = this.calls.reduce((sum, call) => sum + call.duration, 0);
    const averageResponseTime = totalDuration / this.calls.length;

    const slowestCall = this.calls.reduce((slowest, call) => 
      !slowest || call.duration > slowest.duration ? call : slowest
    );

    const fastestCall = this.calls.reduce((fastest, call) => 
      !fastest || call.duration < fastest.duration ? call : fastest
    );

    const timeSpanMinutes = (Date.now() - this.startTime) / (1000 * 60);
    const callsPerMinute = timeSpanMinutes > 0 ? this.calls.length / timeSpanMinutes : 0;

    return {
      totalCalls: this.calls.length,
      authCalls: authCalls.length,
      averageResponseTime: Math.round(averageResponseTime),
      slowestCall,
      fastestCall,
      errorRate: (errorCalls.length / this.calls.length) * 100,
      tokenUsageRate: (tokenCalls.length / this.calls.length) * 100,
      callsPerMinute: Math.round(callsPerMinute * 10) / 10,
      lastResetTime: this.startTime,
    };
  }

  /**
   * Get auth-specific statistics
   */
  getAuthStats(): {
    tokenRefreshCalls: number;
    tokenVerifyCalls: number;
    currentUserCalls: number;
    averageAuthResponseTime: number;
    authErrorRate: number;
  } {
    const authCalls = this.calls.filter(call => call.isAuthRelated);
    
    if (authCalls.length === 0) {
      return {
        tokenRefreshCalls: 0,
        tokenVerifyCalls: 0,
        currentUserCalls: 0,
        averageAuthResponseTime: 0,
        authErrorRate: 0,
      };
    }

    const tokenRefreshCalls = authCalls.filter(call => call.endpoint.includes('/token/refresh/')).length;
    const tokenVerifyCalls = authCalls.filter(call => call.endpoint.includes('/token/verify/')).length;
    const currentUserCalls = authCalls.filter(call => call.endpoint.includes('/current/')).length;

    const authDuration = authCalls.reduce((sum, call) => sum + call.duration, 0);
    const averageAuthResponseTime = authDuration / authCalls.length;

    const authErrors = authCalls.filter(call => call.status >= 400).length;
    const authErrorRate = (authErrors / authCalls.length) * 100;

    return {
      tokenRefreshCalls,
      tokenVerifyCalls,
      currentUserCalls,
      averageAuthResponseTime: Math.round(averageAuthResponseTime),
      authErrorRate: Math.round(authErrorRate * 10) / 10,
    };
  }

  /**
   * Get recent calls (last N calls)
   */
  getRecentCalls(count: number = 10): ApiCallMetrics[] {
    return this.calls.slice(-count);
  }

  /**
   * Get calls by endpoint
   */
  getCallsByEndpoint(endpoint: string): ApiCallMetrics[] {
    return this.calls.filter(call => call.endpoint.includes(endpoint));
  }

  /**
   * Reset statistics
   */
  reset(): void {
    this.calls = [];
    this.startTime = Date.now();
  }

  /**
   * Print performance report
   */
  printReport(): void {
    const stats = this.getStats();
    const authStats = this.getAuthStats();

    console.group('📊 API Performance Report');
    console.log('Overall Stats:', {
      totalCalls: stats.totalCalls,
      authCalls: stats.authCalls,
      averageResponseTime: `${stats.averageResponseTime}ms`,
      errorRate: `${stats.errorRate.toFixed(1)}%`,
      callsPerMinute: stats.callsPerMinute,
    });

    console.log('Auth-Specific Stats:', {
      tokenRefreshCalls: authStats.tokenRefreshCalls,
      tokenVerifyCalls: authStats.tokenVerifyCalls,
      currentUserCalls: authStats.currentUserCalls,
      averageAuthResponseTime: `${authStats.averageAuthResponseTime}ms`,
      authErrorRate: `${authStats.authErrorRate}%`,
    });

    if (stats.slowestCall) {
      console.log('Slowest Call:', {
        endpoint: stats.slowestCall.endpoint,
        method: stats.slowestCall.method,
        duration: `${stats.slowestCall.duration}ms`,
        status: stats.slowestCall.status,
      });
    }

    console.groupEnd();
  }
}

// Export singleton instance
export const apiCallTracker = new ApiCallTracker();

// Auto-print report every 5 minutes in development
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    const stats = apiCallTracker.getStats();
    if (stats.totalCalls > 0) {
      apiCallTracker.printReport();
    }
  }, 5 * 60 * 1000);

  // Add to window for debugging
  (window as any).apiTracker = apiCallTracker;
  console.log('📊 API tracker loaded. Use apiTracker in console for debugging.');
}

export default apiCallTracker;
