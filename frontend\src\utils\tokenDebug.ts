/**
 * Token Debug Utilities
 * Consolidated debugging functions for token management
 */

import { getAuthToken, getRefreshToken, clearAuthTokens } from '../services/api';

/**
 * Debug current token state
 */
export function debugTokens(): void {
  console.group('🔍 Token Debug Information');
  
  const accessToken = getAuthToken();
  const refreshToken = getRefreshToken();
  
  console.log('Access Token:', accessToken ? '✅ Present' : '❌ Missing');
  console.log('Refresh Token:', refreshToken ? '✅ Present' : '❌ Missing');
  
  if (accessToken) {
    try {
      const parts = accessToken.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(atob(parts[1]));
        const now = Math.floor(Date.now() / 1000);
        const isExpired = payload.exp < now;
        
        console.log('Token Details:', {
          userId: payload.user_id,
          username: payload.username,
          expiry: new Date(payload.exp * 1000).toLocaleString(),
          isExpired,
          timeUntilExpiry: isExpired ? 'Expired' : `${Math.floor((payload.exp - now) / 60)} minutes`
        });
      }
    } catch (error) {
      console.error('Error parsing access token:', error);
    }
  }
  
  if (refreshToken) {
    try {
      const parts = refreshToken.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(atob(parts[1]));
        const now = Math.floor(Date.now() / 1000);
        const isExpired = payload.exp < now;
        
        console.log('Refresh Token Details:', {
          expiry: new Date(payload.exp * 1000).toLocaleString(),
          isExpired,
          timeUntilExpiry: isExpired ? 'Expired' : `${Math.floor((payload.exp - now) / 60)} minutes`
        });
      }
    } catch (error) {
      console.error('Error parsing refresh token:', error);
    }
  }
  
  console.groupEnd();
}

/**
 * Clear all authentication tokens
 */
export function clearAllTokens(): void {
  console.log('🗑️ Clearing all authentication tokens');
  clearAuthTokens();
  
  // Also clear any mock tokens
  localStorage.removeItem('enable_mock_auth');
  
  console.log('✅ All tokens cleared');
}

/**
 * Test token validity
 */
export async function testTokenValidity(): Promise<boolean> {
  console.log('🧪 Testing token validity...');
  
  const accessToken = getAuthToken();
  if (!accessToken) {
    console.log('❌ No access token found');
    return false;
  }
  
  try {
    const parts = accessToken.split('.');
    if (parts.length !== 3) {
      console.log('❌ Invalid token format');
      return false;
    }
    
    const payload = JSON.parse(atob(parts[1]));
    const now = Math.floor(Date.now() / 1000);
    const isExpired = payload.exp < now;
    
    if (isExpired) {
      console.log('⚠️ Token is expired');
      return false;
    }
    
    console.log('✅ Token is valid');
    return true;
  } catch (error) {
    console.error('❌ Error validating token:', error);
    return false;
  }
}

/**
 * Get token information for debugging
 */
export function getTokenInfo(): {
  hasAccessToken: boolean;
  hasRefreshToken: boolean;
  accessTokenValid: boolean;
  accessTokenExpired: boolean;
  tokenPayload?: any;
} {
  const accessToken = getAuthToken();
  const refreshToken = getRefreshToken();
  
  let tokenPayload: any = null;
  let accessTokenValid = false;
  let accessTokenExpired = false;
  
  if (accessToken) {
    try {
      const parts = accessToken.split('.');
      if (parts.length === 3) {
        tokenPayload = JSON.parse(atob(parts[1]));
        const now = Math.floor(Date.now() / 1000);
        accessTokenExpired = tokenPayload.exp < now;
        accessTokenValid = !accessTokenExpired;
      }
    } catch (error) {
      console.error('Error parsing token:', error);
    }
  }
  
  return {
    hasAccessToken: !!accessToken,
    hasRefreshToken: !!refreshToken,
    accessTokenValid,
    accessTokenExpired,
    tokenPayload
  };
}

// Export for console debugging
if (typeof window !== 'undefined') {
  (window as any).tokenDebug = {
    debug: debugTokens,
    clear: clearAllTokens,
    test: testTokenValidity,
    info: getTokenInfo
  };
}
