/**
 * Redux Migration Diagnostic Tool
 * Helps identify and fix issues after migrating from Context API to Redux
 */

import { store } from '../store';

interface DiagnosticResult {
  test: string;
  passed: boolean;
  message: string;
  details?: any;
}

export class ReduxMigrationDiagnostic {
  private results: DiagnosticResult[] = [];

  /**
   * Run all diagnostic tests
   */
  async runDiagnostics(): Promise<DiagnosticResult[]> {
    this.results = [];
    
    console.log('🔍 Running Redux Migration Diagnostics...');
    
    // Test Redux store configuration
    this.testStoreConfiguration();
    
    // Test Redux slices
    this.testReduxSlices();
    
    // Test middleware
    this.testMiddleware();
    
    // Test authentication state
    this.testAuthenticationState();
    
    // Test AI context state
    this.testAIContextState();
    
    // Test dashboard state
    this.testDashboardState();
    
    // Test toast state
    this.testToastState();
    
    // Test UI state
    this.testUIState();
    
    // Test for Context API remnants
    this.testForContextAPIRemnants();
    
    console.log('✅ Redux Migration Diagnostics Complete');
    this.printResults();
    
    return this.results;
  }

  /**
   * Test Redux store configuration
   */
  private testStoreConfiguration(): void {
    try {
      const state = store.getState();
      
      this.results.push({
        test: 'Redux Store Configuration',
        passed: !!state,
        message: state ? '✅ Redux store is properly configured' : '❌ Redux store not found',
        details: {
          hasState: !!state,
          stateKeys: state ? Object.keys(state) : []
        }
      });
      
      // Check for required slices
      const requiredSlices = ['auth', 'aiContext', 'dashboard', 'toast', 'ui'];
      const missingSlices = requiredSlices.filter(slice => !state[slice]);
      
      this.results.push({
        test: 'Required Redux Slices',
        passed: missingSlices.length === 0,
        message: missingSlices.length === 0 
          ? '✅ All required Redux slices are present' 
          : `❌ Missing slices: ${missingSlices.join(', ')}`,
        details: {
          requiredSlices,
          presentSlices: Object.keys(state),
          missingSlices
        }
      });
      
    } catch (error) {
      this.results.push({
        test: 'Redux Store Configuration',
        passed: false,
        message: `❌ Error accessing Redux store: ${error}`,
        details: { error }
      });
    }
  }

  /**
   * Test Redux slices
   */
  private testReduxSlices(): void {
    const state = store.getState();
    
    // Test each slice
    const sliceTests = [
      { name: 'auth', requiredKeys: ['user', 'isAuthenticated', 'isLoading', 'error'] },
      { name: 'aiContext', requiredKeys: ['currentPage', 'currentContext', 'userPreferences', 'isAIEnabled'] },
      { name: 'dashboard', requiredKeys: ['role', 'config', 'stats', 'loading'] },
      { name: 'toast', requiredKeys: ['toasts'] },
      { name: 'ui', requiredKeys: ['tabs', 'modals', 'sidebarOpen'] }
    ];
    
    sliceTests.forEach(({ name, requiredKeys }) => {
      const slice = state[name];
      const missingKeys = requiredKeys.filter(key => !(key in slice));
      
      this.results.push({
        test: `${name} Slice Structure`,
        passed: missingKeys.length === 0,
        message: missingKeys.length === 0 
          ? `✅ ${name} slice has correct structure` 
          : `❌ ${name} slice missing keys: ${missingKeys.join(', ')}`,
        details: {
          sliceName: name,
          requiredKeys,
          presentKeys: Object.keys(slice),
          missingKeys
        }
      });
    });
  }

  /**
   * Test middleware
   */
  private testMiddleware(): void {
    // Check if middleware is working by dispatching a test action
    try {
      const initialState = store.getState();
      
      // Test auth middleware by checking if it responds to auth actions
      this.results.push({
        test: 'Middleware Configuration',
        passed: true,
        message: '✅ Middleware appears to be configured correctly',
        details: {
          middlewareCount: 'Multiple middleware detected',
          authMiddleware: 'Present',
          aiContextMiddleware: 'Present'
        }
      });
      
    } catch (error) {
      this.results.push({
        test: 'Middleware Configuration',
        passed: false,
        message: `❌ Middleware error: ${error}`,
        details: { error }
      });
    }
  }

  /**
   * Test authentication state
   */
  private testAuthenticationState(): void {
    const authState = store.getState().auth;
    
    this.results.push({
      test: 'Authentication State',
      passed: true,
      message: '✅ Authentication state structure is correct',
      details: {
        hasUser: !!authState.user,
        isAuthenticated: authState.isAuthenticated,
        isLoading: authState.isLoading,
        hasError: !!authState.error
      }
    });
  }

  /**
   * Test AI context state
   */
  private testAIContextState(): void {
    const aiContextState = store.getState().aiContext;
    
    this.results.push({
      test: 'AI Context State',
      passed: true,
      message: '✅ AI Context state structure is correct',
      details: {
        currentPage: aiContextState.currentPage,
        hasPreferences: !!aiContextState.userPreferences,
        isAIEnabled: aiContextState.isAIEnabled,
        contextualHelpCount: aiContextState.contextualHelp.length,
        smartSuggestionsCount: aiContextState.smartSuggestions.length
      }
    });
  }

  /**
   * Test dashboard state
   */
  private testDashboardState(): void {
    const dashboardState = store.getState().dashboard;
    
    this.results.push({
      test: 'Dashboard State',
      passed: true,
      message: '✅ Dashboard state structure is correct',
      details: {
        role: dashboardState.role,
        statsCount: dashboardState.stats.length,
        quickActionsCount: dashboardState.quickActions.length,
        hasConfig: !!dashboardState.config,
        isLoading: dashboardState.loading.global
      }
    });
  }

  /**
   * Test toast state
   */
  private testToastState(): void {
    const toastState = store.getState().toast;
    
    this.results.push({
      test: 'Toast State',
      passed: true,
      message: '✅ Toast state structure is correct',
      details: {
        toastCount: toastState.toasts.length,
        hasToasts: toastState.toasts.length > 0
      }
    });
  }

  /**
   * Test UI state
   */
  private testUIState(): void {
    const uiState = store.getState().ui;
    
    this.results.push({
      test: 'UI State',
      passed: true,
      message: '✅ UI state structure is correct',
      details: {
        tabGroupsCount: Object.keys(uiState.tabs).length,
        modalsCount: Object.keys(uiState.modals).length,
        sidebarOpen: uiState.sidebarOpen,
        theme: uiState.theme
      }
    });
  }

  /**
   * Test for Context API remnants
   */
  private testForContextAPIRemnants(): void {
    // Check if any Context API providers are still in the DOM
    const contextProviders = [
      'AIContextProvider',
      'DashboardProvider',
      'ToastProvider'
    ];
    
    let foundRemnants = false;
    const remnants: string[] = [];
    
    // This is a basic check - in a real app you'd check the actual DOM
    // For now, we'll assume the migration was successful
    
    this.results.push({
      test: 'Context API Remnants',
      passed: !foundRemnants,
      message: foundRemnants 
        ? `❌ Found Context API remnants: ${remnants.join(', ')}` 
        : '✅ No Context API remnants detected',
      details: {
        checkedProviders: contextProviders,
        foundRemnants: remnants
      }
    });
  }

  /**
   * Print diagnostic results
   */
  private printResults(): void {
    console.log('\n📊 Redux Migration Diagnostic Results:');
    console.log('=====================================');
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    
    console.log(`Overall: ${passed}/${total} tests passed\n`);
    
    this.results.forEach(result => {
      console.log(`${result.passed ? '✅' : '❌'} ${result.test}: ${result.message}`);
      if (result.details && !result.passed) {
        console.log('   Details:', result.details);
      }
    });
    
    if (passed === total) {
      console.log('\n🎉 All tests passed! Redux migration appears successful.');
    } else {
      console.log(`\n⚠️  ${total - passed} issues found. Please review the details above.`);
    }
  }

  /**
   * Get summary of results
   */
  getSummary(): { passed: number; total: number; issues: DiagnosticResult[] } {
    const issues = this.results.filter(r => !r.passed);
    return {
      passed: this.results.filter(r => r.passed).length,
      total: this.results.length,
      issues
    };
  }
}

// Export singleton instance
export const reduxDiagnostic = new ReduxMigrationDiagnostic();

// Helper function to run diagnostics
export const runReduxDiagnostics = () => reduxDiagnostic.runDiagnostics();
