import React from 'react';
import { UnifiedDashboard } from '../../components/dashboard/unified';

/**
 * Moderator Dashboard Page
 * Now uses the unified dashboard architecture with Redux state management
 *
 * This page has been migrated from Context API providers to Redux-based
 * state management for role-specific data and functionality.
 */
const ModeratorDashboardPage: React.FC = () => {
  return (
    <UnifiedDashboard
      role="moderator"
      className="moderator-dashboard"
    />
  );
};

export default ModeratorDashboardPage;
