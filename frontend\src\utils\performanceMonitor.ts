/**
 * Performance Monitor
 * Comprehensive performance monitoring for middleware operations and auth processes
 */

import { authLogger } from './authLogger';

interface PerformanceMetric {
  id: string;
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  category: 'middleware' | 'auth' | 'token' | 'api' | 'sync' | 'ui';
  metadata: any;
  tags: string[];
}

interface PerformanceReport {
  totalMetrics: number;
  averageDuration: number;
  slowestOperation: PerformanceMetric | null;
  fastestOperation: PerformanceMetric | null;
  categorySummary: Record<string, {
    count: number;
    averageDuration: number;
    totalDuration: number;
  }>;
  performanceIssues: PerformanceIssue[];
  recommendations: string[];
}

interface PerformanceIssue {
  type: 'slow_operation' | 'frequent_calls' | 'memory_leak' | 'blocking_operation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedMetrics: string[];
  recommendation: string;
}

interface PerformanceThresholds {
  middleware: {
    fast: number;
    normal: number;
    slow: number;
    critical: number;
  };
  auth: {
    fast: number;
    normal: number;
    slow: number;
    critical: number;
  };
  token: {
    fast: number;
    normal: number;
    slow: number;
    critical: number;
  };
  api: {
    fast: number;
    normal: number;
    slow: number;
    critical: number;
  };
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private completedMetrics: PerformanceMetric[] = [];
  private maxMetrics = 1000;
  
  private thresholds: PerformanceThresholds = {
    middleware: { fast: 5, normal: 20, slow: 100, critical: 500 },
    auth: { fast: 50, normal: 200, slow: 1000, critical: 5000 },
    token: { fast: 10, normal: 50, slow: 200, critical: 1000 },
    api: { fast: 100, normal: 500, slow: 2000, critical: 10000 }
  };

  /**
   * Start measuring performance for an operation
   */
  startMeasurement(name: string, category: PerformanceMetric['category'], metadata: any = {}, tags: string[] = []): string {
    const id = this.generateId();
    const metric: PerformanceMetric = {
      id,
      name,
      startTime: performance.now(),
      category,
      metadata,
      tags
    };

    this.metrics.set(id, metric);
    
    // Log start of measurement
    authLogger.logPerformanceMetric(`${category}_start`, metric.startTime, {
      name,
      id,
      metadata,
      tags
    });

    return id;
  }

  /**
   * End measurement and calculate duration
   */
  endMeasurement(id: string, additionalMetadata: any = {}): PerformanceMetric | null {
    const metric = this.metrics.get(id);
    if (!metric) {
      authLogger.logWarning('performance', 'measurement_not_found', `Measurement ${id} not found`);
      return null;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;
    metric.metadata = { ...metric.metadata, ...additionalMetadata };

    // Move to completed metrics
    this.metrics.delete(id);
    this.completedMetrics.push(metric);

    // Maintain max metrics
    if (this.completedMetrics.length > this.maxMetrics) {
      this.completedMetrics = this.completedMetrics.slice(-this.maxMetrics);
    }

    // Log completion
    authLogger.logPerformanceMetric(`${metric.category}_complete`, metric.duration, {
      name: metric.name,
      id: metric.id,
      duration: metric.duration,
      metadata: metric.metadata,
      performanceLevel: this.getPerformanceLevel(metric)
    });

    // Check for performance issues
    this.checkPerformanceIssues(metric);

    return metric;
  }

  /**
   * Measure a function execution
   */
  async measureFunction<T>(
    name: string, 
    category: PerformanceMetric['category'], 
    fn: () => Promise<T> | T,
    metadata: any = {},
    tags: string[] = []
  ): Promise<T> {
    const id = this.startMeasurement(name, category, metadata, tags);
    
    try {
      const result = await fn();
      this.endMeasurement(id, { success: true });
      return result;
    } catch (error) {
      this.endMeasurement(id, { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * Measure middleware operation
   */
  measureMiddleware<T>(name: string, fn: () => T, metadata: any = {}): T {
    const startTime = performance.now();
    
    try {
      const result = fn();
      const duration = performance.now() - startTime;
      
      authLogger.logMiddlewareOperation(name, duration, {
        ...metadata,
        success: true
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      authLogger.logMiddlewareOperation(name, duration, {
        ...metadata,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  }

  /**
   * Get performance report
   */
  getPerformanceReport(timeRange?: { start: number; end: number }): PerformanceReport {
    let metricsToAnalyze = this.completedMetrics;
    
    if (timeRange) {
      metricsToAnalyze = this.completedMetrics.filter(
        metric => metric.startTime >= timeRange.start && metric.startTime <= timeRange.end
      );
    }

    const totalMetrics = metricsToAnalyze.length;
    const durations = metricsToAnalyze.map(m => m.duration!).filter(d => d !== undefined);
    
    const averageDuration = durations.length > 0 
      ? durations.reduce((sum, d) => sum + d, 0) / durations.length 
      : 0;

    const slowestOperation = metricsToAnalyze.reduce((slowest, current) => 
      !slowest || (current.duration! > slowest.duration!) ? current : slowest, 
      null as PerformanceMetric | null
    );

    const fastestOperation = metricsToAnalyze.reduce((fastest, current) => 
      !fastest || (current.duration! < fastest.duration!) ? current : fastest, 
      null as PerformanceMetric | null
    );

    // Category summary
    const categorySummary: Record<string, any> = {};
    metricsToAnalyze.forEach(metric => {
      if (!categorySummary[metric.category]) {
        categorySummary[metric.category] = {
          count: 0,
          totalDuration: 0,
          averageDuration: 0
        };
      }
      
      categorySummary[metric.category].count++;
      categorySummary[metric.category].totalDuration += metric.duration!;
    });

    // Calculate averages
    Object.keys(categorySummary).forEach(category => {
      const summary = categorySummary[category];
      summary.averageDuration = summary.totalDuration / summary.count;
    });

    // Identify performance issues
    const performanceIssues = this.identifyPerformanceIssues(metricsToAnalyze);
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(performanceIssues, categorySummary);

    return {
      totalMetrics,
      averageDuration,
      slowestOperation,
      fastestOperation,
      categorySummary,
      performanceIssues,
      recommendations
    };
  }

  /**
   * Get metrics by category
   */
  getMetricsByCategory(category: PerformanceMetric['category']): PerformanceMetric[] {
    return this.completedMetrics.filter(metric => metric.category === category);
  }

  /**
   * Get slow operations
   */
  getSlowOperations(threshold?: number): PerformanceMetric[] {
    return this.completedMetrics.filter(metric => {
      const categoryThreshold = threshold || this.thresholds[metric.category]?.slow || 100;
      return metric.duration! > categoryThreshold;
    });
  }

  /**
   * Clear metrics
   */
  clearMetrics(): void {
    this.metrics.clear();
    this.completedMetrics = [];
    
    authLogger.log('info', 'performance', 'metrics_cleared', {
      clearedAt: Date.now()
    });
  }

  /**
   * Update thresholds
   */
  updateThresholds(newThresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    
    authLogger.log('info', 'performance', 'thresholds_updated', {
      newThresholds: this.thresholds
    });
  }

  /**
   * Export metrics
   */
  exportMetrics(): string {
    return JSON.stringify({
      exportTime: Date.now(),
      thresholds: this.thresholds,
      activeMetrics: Array.from(this.metrics.values()),
      completedMetrics: this.completedMetrics,
      report: this.getPerformanceReport()
    }, null, 2);
  }

  /**
   * Get real-time performance stats
   */
  getRealTimeStats(): any {
    const recentMetrics = this.completedMetrics.slice(-50); // Last 50 operations
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    const recentOperations = recentMetrics.filter(m => m.startTime > oneMinuteAgo);

    return {
      activeOperations: this.metrics.size,
      recentOperations: recentOperations.length,
      averageRecentDuration: recentOperations.length > 0
        ? recentOperations.reduce((sum, m) => sum + m.duration!, 0) / recentOperations.length
        : 0,
      slowRecentOperations: recentOperations.filter(m => this.getPerformanceLevel(m) === 'slow').length,
      criticalRecentOperations: recentOperations.filter(m => this.getPerformanceLevel(m) === 'critical').length
    };
  }

  /**
   * Private helper methods
   */
  private generateId(): string {
    return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getPerformanceLevel(metric: PerformanceMetric): string {
    const thresholds = this.thresholds[metric.category];
    if (!thresholds || !metric.duration) return 'unknown';

    if (metric.duration <= thresholds.fast) return 'fast';
    if (metric.duration <= thresholds.normal) return 'normal';
    if (metric.duration <= thresholds.slow) return 'slow';
    return 'critical';
  }

  private checkPerformanceIssues(metric: PerformanceMetric): void {
    const level = this.getPerformanceLevel(metric);
    
    if (level === 'critical') {
      authLogger.logError('performance', 'critical_performance', 
        `Critical performance issue detected: ${metric.name} took ${metric.duration}ms`, {
          metric,
          threshold: this.thresholds[metric.category]?.critical
        });
    } else if (level === 'slow') {
      authLogger.logWarning('performance', 'slow_performance', 
        `Slow operation detected: ${metric.name} took ${metric.duration}ms`, {
          metric,
          threshold: this.thresholds[metric.category]?.slow
        });
    }
  }

  private identifyPerformanceIssues(metrics: PerformanceMetric[]): PerformanceIssue[] {
    const issues: PerformanceIssue[] = [];

    // Check for slow operations
    const slowOps = metrics.filter(m => this.getPerformanceLevel(m) === 'slow');
    if (slowOps.length > 0) {
      issues.push({
        type: 'slow_operation',
        severity: 'medium',
        description: `${slowOps.length} slow operations detected`,
        affectedMetrics: slowOps.map(m => m.id),
        recommendation: 'Optimize slow operations or increase thresholds if acceptable'
      });
    }

    // Check for critical operations
    const criticalOps = metrics.filter(m => this.getPerformanceLevel(m) === 'critical');
    if (criticalOps.length > 0) {
      issues.push({
        type: 'slow_operation',
        severity: 'critical',
        description: `${criticalOps.length} critical performance issues detected`,
        affectedMetrics: criticalOps.map(m => m.id),
        recommendation: 'Immediate optimization required for critical operations'
      });
    }

    // Check for frequent calls
    const operationCounts = new Map<string, number>();
    metrics.forEach(m => {
      const count = operationCounts.get(m.name) || 0;
      operationCounts.set(m.name, count + 1);
    });

    operationCounts.forEach((count, operation) => {
      if (count > 50) { // More than 50 calls
        issues.push({
          type: 'frequent_calls',
          severity: count > 100 ? 'high' : 'medium',
          description: `Operation "${operation}" called ${count} times`,
          affectedMetrics: metrics.filter(m => m.name === operation).map(m => m.id),
          recommendation: 'Consider caching or debouncing frequent operations'
        });
      }
    });

    return issues;
  }

  private generateRecommendations(issues: PerformanceIssue[], categorySummary: any): string[] {
    const recommendations: string[] = [];

    // General recommendations based on issues
    if (issues.some(i => i.type === 'slow_operation')) {
      recommendations.push('Profile and optimize slow operations');
    }

    if (issues.some(i => i.type === 'frequent_calls')) {
      recommendations.push('Implement caching or debouncing for frequently called operations');
    }

    // Category-specific recommendations
    if (categorySummary.middleware?.averageDuration > 50) {
      recommendations.push('Middleware operations are slower than expected - consider optimization');
    }

    if (categorySummary.token?.count > 100) {
      recommendations.push('High number of token operations - implement better caching');
    }

    if (categorySummary.auth?.averageDuration > 1000) {
      recommendations.push('Authentication operations are slow - check network and server performance');
    }

    return recommendations;
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Export for console debugging
if (typeof window !== 'undefined') {
  (window as any).performanceMonitor = performanceMonitor;
  console.log('📊 Performance monitor loaded. Use performanceMonitor in console.');
}

export default performanceMonitor;
