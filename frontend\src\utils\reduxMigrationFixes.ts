/**
 * Redux Migration Fixes
 * Provides fixes for common issues after migrating from Context API to Redux
 */

import { store } from '../store';
import { clearAuth, getCurrentUser } from '../store/authSlice';
import { updateCurrentPage, loadUserPreferences } from '../store/aiContextSlice';
import { setRole } from '../store/dashboardSlice';

export class ReduxMigrationFixes {
  
  /**
   * Fix authentication state synchronization
   */
  static fixAuthState(): void {
    console.log('🔧 Fixing authentication state...');
    
    try {
      const authState = store.getState().auth;
      
      // If we have inconsistent auth state, try to fix it
      if (authState.isAuthenticated && !authState.user) {
        console.log('🔧 Auth state inconsistent, fetching current user...');
        store.dispatch(getCurrentUser());
      }
      
      console.log('✅ Authentication state fix completed');
    } catch (error) {
      console.error('❌ Error fixing auth state:', error);
    }
  }

  /**
   * Fix AI context initialization
   */
  static fixAIContext(): void {
    console.log('🔧 Fixing AI context...');
    
    try {
      const { auth, aiContext } = store.getState();
      
      // Initialize AI context if user is authenticated
      if (auth.user && !aiContext.userId) {
        console.log('🔧 Setting AI context user ID...');
        store.dispatch(loadUserPreferences());
      }
      
      // Set current page if not set
      if (!aiContext.currentPage && typeof window !== 'undefined') {
        console.log('🔧 Setting current page in AI context...');
        store.dispatch(updateCurrentPage(window.location.pathname));
      }
      
      console.log('✅ AI context fix completed');
    } catch (error) {
      console.error('❌ Error fixing AI context:', error);
    }
  }

  /**
   * Fix dashboard state
   */
  static fixDashboardState(): void {
    console.log('🔧 Fixing dashboard state...');
    
    try {
      const { auth, dashboard } = store.getState();
      
      // Set dashboard role based on user
      if (auth.user && dashboard.role === 'user') {
        const userRole = auth.user.user_type || 'user';
        console.log('🔧 Setting dashboard role:', userRole);
        store.dispatch(setRole(userRole as any));
      }
      
      console.log('✅ Dashboard state fix completed');
    } catch (error) {
      console.error('❌ Error fixing dashboard state:', error);
    }
  }

  /**
   * Clear any corrupted state
   */
  static clearCorruptedState(): void {
    console.log('🔧 Clearing potentially corrupted state...');
    
    try {
      // Clear localStorage items that might be corrupted
      const itemsToCheck = [
        'aiPreferences',
        'dashboardConfig',
        'uiState'
      ];
      
      itemsToCheck.forEach(item => {
        try {
          const stored = localStorage.getItem(item);
          if (stored) {
            JSON.parse(stored); // Test if it's valid JSON
          }
        } catch (error) {
          console.log(`🔧 Removing corrupted localStorage item: ${item}`);
          localStorage.removeItem(item);
        }
      });
      
      console.log('✅ Corrupted state cleanup completed');
    } catch (error) {
      console.error('❌ Error clearing corrupted state:', error);
    }
  }

  /**
   * Reset Redux state to initial values
   */
  static resetReduxState(): void {
    console.log('🔧 Resetting Redux state...');
    
    try {
      // This is a more aggressive fix - only use if needed
      console.warn('⚠️ This will clear all Redux state. Use with caution.');
      
      // Clear auth state
      store.dispatch(clearAuth());
      
      // Reload the page to reinitialize everything
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
      
    } catch (error) {
      console.error('❌ Error resetting Redux state:', error);
    }
  }

  /**
   * Run all fixes
   */
  static runAllFixes(): void {
    console.log('🔧 Running all Redux migration fixes...');
    
    this.clearCorruptedState();
    this.fixAuthState();
    this.fixAIContext();
    this.fixDashboardState();
    
    console.log('✅ All Redux migration fixes completed');
  }

  /**
   * Quick diagnostic and fix
   */
  static quickFix(): void {
    console.log('🔧 Running quick Redux migration fix...');
    
    try {
      const state = store.getState();
      
      // Check if state exists
      if (!state) {
        console.error('❌ Redux state not found');
        return;
      }
      
      // Check for common issues
      const issues = [];
      
      if (state.auth.isAuthenticated && !state.auth.user) {
        issues.push('auth-user-mismatch');
      }
      
      if (state.auth.user && !state.aiContext.userId) {
        issues.push('ai-context-user-missing');
      }
      
      if (state.auth.user && state.dashboard.role === 'user' && state.auth.user.user_type !== 'user') {
        issues.push('dashboard-role-mismatch');
      }
      
      // Fix identified issues
      if (issues.includes('auth-user-mismatch')) {
        console.log('🔧 Fixing auth-user mismatch...');
        store.dispatch(getCurrentUser());
      }
      
      if (issues.includes('ai-context-user-missing')) {
        console.log('🔧 Fixing AI context user...');
        store.dispatch(loadUserPreferences());
      }
      
      if (issues.includes('dashboard-role-mismatch')) {
        console.log('🔧 Fixing dashboard role...');
        const userRole = state.auth.user.user_type || 'user';
        store.dispatch(setRole(userRole as any));
      }
      
      if (issues.length === 0) {
        console.log('✅ No issues found - Redux state looks good!');
      } else {
        console.log(`✅ Fixed ${issues.length} issues:`, issues);
      }
      
    } catch (error) {
      console.error('❌ Error in quick fix:', error);
    }
  }
}

// Export for easy use
export const fixReduxMigration = ReduxMigrationFixes.quickFix;
export const runAllReduxFixes = ReduxMigrationFixes.runAllFixes;
