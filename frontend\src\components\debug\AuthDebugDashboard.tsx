/**
 * Authentication Debug Dashboard
 * Comprehensive debugging interface for authentication, tokens, and state synchronization
 */

import React, { useState, useEffect } from 'react';
import { useAppSelector } from '../../store/hooks';
import { authLogger } from '../../utils/authLogger';
import { performanceMonitor } from '../../utils/performanceMonitor';
import { stateSynchronizer } from '../../utils/stateSynchronizer';
import { tokenExpirationHandler } from '../../utils/tokenExpirationHandler';
import { getAuthToken, getRefreshToken, getTokenExpiry } from '../../services/api';

interface DebugTab {
  id: string;
  label: string;
  component: React.ComponentType;
}

const AuthDebugDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isVisible, setIsVisible] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [realTimeData, setRealTimeData] = useState<any>({});

  const authState = useAppSelector(state => state.auth);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  useEffect(() => {
    if (isVisible) {
      // Start real-time updates
      const interval = setInterval(updateRealTimeData, 1000);
      setRefreshInterval(interval);
      
      return () => {
        if (interval) clearInterval(interval);
      };
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }
  }, [isVisible]);

  const updateRealTimeData = () => {
    setRealTimeData({
      authState,
      tokenStatus: tokenExpirationHandler.getTokenStatus(),
      performanceStats: performanceMonitor.getRealTimeStats(),
      recentLogs: authLogger.getRecentLogs(10),
      syncHistory: stateSynchronizer.getSyncHistory().slice(-5)
    });
  };

  const tabs: DebugTab[] = [
    { id: 'overview', label: 'Overview', component: OverviewTab },
    { id: 'auth', label: 'Auth State', component: AuthStateTab },
    { id: 'tokens', label: 'Tokens', component: TokensTab },
    { id: 'performance', label: 'Performance', component: PerformanceTab },
    { id: 'logs', label: 'Logs', component: LogsTab },
    { id: 'sync', label: 'State Sync', component: StateSyncTab }
  ];

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-50"
        title="Open Auth Debug Dashboard"
      >
        🔧
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-bold text-gray-900">Auth Debug Dashboard</h2>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-500 hover:text-gray-700 text-xl"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === tab.id
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4">
          {tabs.map(tab => (
            activeTab === tab.id && (
              <tab.component key={tab.id} data={realTimeData} />
            )
          ))}
        </div>
      </div>
    </div>
  );
};

// Overview Tab Component
const OverviewTab: React.FC<{ data: any }> = ({ data }) => {
  const { authState, tokenStatus, performanceStats } = data;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Auth Status */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Authentication</h3>
          <div className="space-y-1 text-sm">
            <div className={`flex items-center ${authState?.isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
              <span className="w-2 h-2 rounded-full bg-current mr-2"></span>
              {authState?.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
            </div>
            <div>User: {authState?.user?.username || 'None'}</div>
            <div>Role: {authState?.user?.role || 'None'}</div>
            <div>Loading: {authState?.isLoading ? 'Yes' : 'No'}</div>
          </div>
        </div>

        {/* Token Status */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Token Status</h3>
          <div className="space-y-1 text-sm">
            <div className={`flex items-center ${tokenStatus?.hasToken ? 'text-green-600' : 'text-red-600'}`}>
              <span className="w-2 h-2 rounded-full bg-current mr-2"></span>
              {tokenStatus?.hasToken ? 'Token Present' : 'No Token'}
            </div>
            <div>Expired: {tokenStatus?.isExpired ? 'Yes' : 'No'}</div>
            <div>Warning: {tokenStatus?.warningLevel || 'None'}</div>
            {tokenStatus?.timeUntilExpiry && (
              <div>Expires in: {Math.round(tokenStatus.timeUntilExpiry / 1000 / 60)} min</div>
            )}
          </div>
        </div>

        {/* Performance */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Performance</h3>
          <div className="space-y-1 text-sm">
            <div>Active Ops: {performanceStats?.activeOperations || 0}</div>
            <div>Recent Ops: {performanceStats?.recentOperations || 0}</div>
            <div>Avg Duration: {Math.round(performanceStats?.averageRecentDuration || 0)}ms</div>
            <div className={`${performanceStats?.slowRecentOperations > 0 ? 'text-yellow-600' : 'text-green-600'}`}>
              Slow Ops: {performanceStats?.slowRecentOperations || 0}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-3">Quick Actions</h3>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => stateSynchronizer.forceSynchronization()}
            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
          >
            Force Sync
          </button>
          <button
            onClick={() => authLogger.clearLogs()}
            className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700"
          >
            Clear Logs
          </button>
          <button
            onClick={() => performanceMonitor.clearMetrics()}
            className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700"
          >
            Clear Metrics
          </button>
          <button
            onClick={() => {
              const report = performanceMonitor.getPerformanceReport();
              console.log('Performance Report:', report);
            }}
            className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
          >
            Log Report
          </button>
        </div>
      </div>
    </div>
  );
};

// Auth State Tab Component
const AuthStateTab: React.FC<{ data: any }> = ({ data }) => {
  const { authState } = data;

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-3">Redux Auth State</h3>
        <pre className="text-xs bg-white p-3 rounded border overflow-auto">
          {JSON.stringify(authState, null, 2)}
        </pre>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-3">localStorage Tokens</h3>
        <div className="space-y-2 text-sm">
          <div>
            <strong>Access Token:</strong> {getAuthToken() ? 'Present' : 'Missing'}
          </div>
          <div>
            <strong>Refresh Token:</strong> {getRefreshToken() ? 'Present' : 'Missing'}
          </div>
          <div>
            <strong>Token Expiry:</strong> {getTokenExpiry() ? new Date(getTokenExpiry()!).toLocaleString() : 'Unknown'}
          </div>
        </div>
      </div>
    </div>
  );
};

// Tokens Tab Component
const TokensTab: React.FC<{ data: any }> = ({ data }) => {
  const { tokenStatus } = data;

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-3">Token Status</h3>
        <pre className="text-xs bg-white p-3 rounded border overflow-auto">
          {JSON.stringify(tokenStatus, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Performance Tab Component
const PerformanceTab: React.FC<{ data: any }> = ({ data }) => {
  const { performanceStats } = data;
  const [report, setReport] = useState<any>(null);

  useEffect(() => {
    setReport(performanceMonitor.getPerformanceReport());
  }, []);

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-3">Real-time Stats</h3>
        <pre className="text-xs bg-white p-3 rounded border overflow-auto">
          {JSON.stringify(performanceStats, null, 2)}
        </pre>
      </div>

      {report && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-3">Performance Report</h3>
          <pre className="text-xs bg-white p-3 rounded border overflow-auto max-h-64">
            {JSON.stringify(report, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

// Logs Tab Component
const LogsTab: React.FC<{ data: any }> = ({ data }) => {
  const { recentLogs } = data;

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-3">Recent Logs</h3>
        <div className="space-y-2 max-h-96 overflow-auto">
          {recentLogs?.map((log: any, index: number) => (
            <div key={index} className="text-xs bg-white p-2 rounded border">
              <div className="flex items-center justify-between mb-1">
                <span className={`px-2 py-1 rounded text-xs ${
                  log.level === 'error' ? 'bg-red-100 text-red-800' :
                  log.level === 'warn' ? 'bg-yellow-100 text-yellow-800' :
                  log.level === 'info' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {log.level.toUpperCase()}
                </span>
                <span className="text-gray-500">
                  {new Date(log.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <div className="font-medium">[{log.category}] {log.action}</div>
              {log.details && (
                <pre className="mt-1 text-xs text-gray-600 overflow-auto">
                  {JSON.stringify(log.details, null, 2)}
                </pre>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// State Sync Tab Component
const StateSyncTab: React.FC<{ data: any }> = ({ data }) => {
  const { syncHistory } = data;

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-3">Sync History</h3>
        <div className="space-y-2 max-h-96 overflow-auto">
          {syncHistory?.map((sync: any, index: number) => (
            <div key={index} className="text-xs bg-white p-2 rounded border">
              <div className="flex items-center justify-between mb-1">
                <span className={`px-2 py-1 rounded text-xs ${
                  sync.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {sync.success ? 'SUCCESS' : 'FAILED'}
                </span>
                <span className="text-gray-500">
                  {new Date(sync.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <div className="font-medium">{sync.action}</div>
              <div className="text-gray-600">{sync.details}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AuthDebugDashboard;
