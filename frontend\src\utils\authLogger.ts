/**
 * Enhanced Authentication Logger
 * Comprehensive logging system for authentication state changes and operations
 */

interface LogEntry {
  timestamp: number;
  level: 'debug' | 'info' | 'warn' | 'error';
  category: 'auth' | 'token' | 'state' | 'middleware' | 'sync' | 'performance';
  action: string;
  details: any;
  userId?: string;
  sessionId: string;
  stackTrace?: string;
}

interface LoggerConfig {
  enabled: boolean;
  level: 'debug' | 'info' | 'warn' | 'error';
  maxEntries: number;
  persistToStorage: boolean;
  includeStackTrace: boolean;
  categories: {
    auth: boolean;
    token: boolean;
    state: boolean;
    middleware: boolean;
    sync: boolean;
    performance: boolean;
  };
}

class AuthLogger {
  private logs: LogEntry[] = [];
  private sessionId: string;
  private config: LoggerConfig = {
    enabled: true,
    level: 'debug',
    maxEntries: 1000,
    persistToStorage: process.env.NODE_ENV === 'development',
    includeStackTrace: process.env.NODE_ENV === 'development',
    categories: {
      auth: true,
      token: true,
      state: true,
      middleware: true,
      sync: true,
      performance: true
    }
  };

  constructor() {
    this.sessionId = this.generateSessionId();
    this.loadPersistedLogs();
    
    // Log session start
    this.log('info', 'auth', 'session_start', {
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });
  }

  /**
   * Main logging method
   */
  log(level: LogEntry['level'], category: LogEntry['category'], action: string, details: any = {}, userId?: string): void {
    if (!this.config.enabled || !this.config.categories[category]) {
      return;
    }

    // Check log level
    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.config.level);
    const logLevelIndex = levels.indexOf(level);
    
    if (logLevelIndex < currentLevelIndex) {
      return;
    }

    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      category,
      action,
      details: this.sanitizeDetails(details),
      userId,
      sessionId: this.sessionId
    };

    // Add stack trace for errors and warnings in development
    if (this.config.includeStackTrace && (level === 'error' || level === 'warn')) {
      entry.stackTrace = new Error().stack;
    }

    // Add to logs
    this.logs.push(entry);

    // Maintain max entries
    if (this.logs.length > this.config.maxEntries) {
      this.logs = this.logs.slice(-this.config.maxEntries);
    }

    // Console output in development
    if (process.env.NODE_ENV === 'development') {
      this.consoleOutput(entry);
    }

    // Persist to storage if enabled
    if (this.config.persistToStorage) {
      this.persistLogs();
    }
  }

  /**
   * Authentication-specific logging methods
   */
  logAuthStateChange(action: string, oldState: any, newState: any, userId?: string): void {
    this.log('info', 'auth', action, {
      oldState: this.sanitizeAuthState(oldState),
      newState: this.sanitizeAuthState(newState),
      stateChanged: JSON.stringify(oldState) !== JSON.stringify(newState)
    }, userId);
  }

  logTokenOperation(action: string, details: any = {}, userId?: string): void {
    this.log('info', 'token', action, {
      ...details,
      hasToken: !!details.token,
      tokenLength: details.token?.length || 0,
      // Remove actual token value for security
      token: details.token ? '[REDACTED]' : null
    }, userId);
  }

  logMiddlewareOperation(action: string, duration: number, details: any = {}): void {
    this.log('debug', 'middleware', action, {
      ...details,
      duration,
      performance: {
        fast: duration < 10,
        normal: duration >= 10 && duration < 100,
        slow: duration >= 100
      }
    });
  }

  logStateSync(action: string, result: any, duration: number): void {
    this.log('info', 'sync', action, {
      ...result,
      duration,
      success: result.success || result.isConsistent
    });
  }

  logPerformanceMetric(metric: string, value: number, context: any = {}): void {
    this.log('debug', 'performance', metric, {
      value,
      unit: 'ms',
      context,
      timestamp: Date.now()
    });
  }

  logError(category: LogEntry['category'], action: string, error: Error | string, context: any = {}): void {
    this.log('error', category, action, {
      error: error instanceof Error ? {
        message: error.message,
        name: error.name,
        stack: error.stack
      } : error,
      context
    });
  }

  logWarning(category: LogEntry['category'], action: string, message: string, context: any = {}): void {
    this.log('warn', category, action, {
      message,
      context
    });
  }

  /**
   * Query and analysis methods
   */
  getLogsByCategory(category: LogEntry['category']): LogEntry[] {
    return this.logs.filter(log => log.category === category);
  }

  getLogsByLevel(level: LogEntry['level']): LogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  getLogsByTimeRange(startTime: number, endTime: number): LogEntry[] {
    return this.logs.filter(log => log.timestamp >= startTime && log.timestamp <= endTime);
  }

  getLogsByUser(userId: string): LogEntry[] {
    return this.logs.filter(log => log.userId === userId);
  }

  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logs.slice(-count);
  }

  /**
   * Performance analysis
   */
  getPerformanceMetrics(): any {
    const perfLogs = this.getLogsByCategory('performance');
    const middlewareLogs = this.getLogsByCategory('middleware');
    
    const tokenOperations = this.getLogsByCategory('token');
    const authOperations = this.getLogsByCategory('auth');
    
    return {
      totalLogs: this.logs.length,
      categories: {
        performance: perfLogs.length,
        middleware: middlewareLogs.length,
        token: tokenOperations.length,
        auth: authOperations.length
      },
      averageMiddlewareDuration: this.calculateAverageDuration(middlewareLogs),
      slowOperations: middlewareLogs.filter(log => log.details.duration > 100),
      errorCount: this.getLogsByLevel('error').length,
      warningCount: this.getLogsByLevel('warn').length,
      sessionDuration: Date.now() - this.logs[0]?.timestamp || 0
    };
  }

  /**
   * Export logs for analysis
   */
  exportLogs(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      return this.exportAsCSV();
    }
    
    return JSON.stringify({
      sessionId: this.sessionId,
      exportTime: new Date().toISOString(),
      config: this.config,
      logs: this.logs
    }, null, 2);
  }

  /**
   * Clear logs
   */
  clearLogs(): void {
    this.logs = [];
    if (this.config.persistToStorage) {
      localStorage.removeItem('auth_logs');
    }
    
    this.log('info', 'auth', 'logs_cleared', {
      clearedAt: new Date().toISOString()
    });
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    this.log('info', 'auth', 'config_updated', {
      newConfig: this.config
    });
  }

  /**
   * Private helper methods
   */
  private generateSessionId(): string {
    return `auth_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sanitizeDetails(details: any): any {
    if (!details || typeof details !== 'object') {
      return details;
    }

    const sanitized = { ...details };
    
    // Remove sensitive information
    const sensitiveKeys = ['password', 'token', 'refreshToken', 'accessToken', 'secret'];
    sensitiveKeys.forEach(key => {
      if (sanitized[key]) {
        sanitized[key] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  private sanitizeAuthState(state: any): any {
    if (!state) return state;
    
    return {
      isAuthenticated: state.isAuthenticated,
      isLoading: state.isLoading,
      hasUser: !!state.user,
      userRole: state.user?.role,
      hasError: !!state.error,
      errorType: state.error ? typeof state.error : null
    };
  }

  private consoleOutput(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString();
    const prefix = `[${timestamp}] [${entry.level.toUpperCase()}] [${entry.category}]`;
    
    const message = `${prefix} ${entry.action}`;
    
    switch (entry.level) {
      case 'error':
        console.error(message, entry.details);
        break;
      case 'warn':
        console.warn(message, entry.details);
        break;
      case 'info':
        console.info(message, entry.details);
        break;
      case 'debug':
        console.debug(message, entry.details);
        break;
    }
  }

  private calculateAverageDuration(logs: LogEntry[]): number {
    const durations = logs
      .filter(log => log.details.duration)
      .map(log => log.details.duration);
    
    if (durations.length === 0) return 0;
    
    return durations.reduce((sum, duration) => sum + duration, 0) / durations.length;
  }

  private exportAsCSV(): string {
    const headers = ['timestamp', 'level', 'category', 'action', 'userId', 'details'];
    const rows = this.logs.map(log => [
      new Date(log.timestamp).toISOString(),
      log.level,
      log.category,
      log.action,
      log.userId || '',
      JSON.stringify(log.details)
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  private persistLogs(): void {
    try {
      const logsToStore = this.logs.slice(-100); // Store only last 100 logs
      localStorage.setItem('auth_logs', JSON.stringify(logsToStore));
    } catch (error) {
      console.warn('Failed to persist auth logs:', error);
    }
  }

  private loadPersistedLogs(): void {
    try {
      const stored = localStorage.getItem('auth_logs');
      if (stored) {
        const parsedLogs = JSON.parse(stored);
        if (Array.isArray(parsedLogs)) {
          this.logs = parsedLogs;
        }
      }
    } catch (error) {
      console.warn('Failed to load persisted auth logs:', error);
    }
  }
}

// Export singleton instance
export const authLogger = new AuthLogger();

// Export for console debugging
if (typeof window !== 'undefined') {
  (window as any).authLogger = authLogger;
  console.log('📝 Auth logger loaded. Use authLogger in console.');
}

export default authLogger;
