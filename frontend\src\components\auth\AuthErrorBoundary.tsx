/**
 * Enhanced Authentication Error Boundary
 * Handles authentication failures and provides recovery mechanisms
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { store } from '../../store';
import { clearAuth } from '../../store/authSlice';
import { clearAuthTokens } from '../../services/api';
import { stateSynchronizer } from '../../utils/stateSynchronizer';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  isRecovering: boolean;
  recoveryAttempts: number;
  lastErrorTime: number;
}

interface AuthErrorDetails {
  type: 'token_expired' | 'token_invalid' | 'network_error' | 'auth_state_error' | 'unknown';
  message: string;
  canRecover: boolean;
  recoveryAction: string;
}

export class AuthErrorBoundary extends Component<Props, State> {
  private maxRecoveryAttempts = 3;
  private recoveryDebounceMs = 1000;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isRecovering: false,
      recoveryAttempts: 0,
      lastErrorTime: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      lastErrorTime: Date.now(),
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 Auth Error Boundary caught error:', error, errorInfo);
    
    this.setState({
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Analyze and potentially auto-recover from auth errors
    this.analyzeAndRecover(error);
  }

  /**
   * Analyze error and attempt recovery
   */
  private async analyzeAndRecover(error: Error): Promise<void> {
    const errorDetails = this.analyzeAuthError(error);
    
    console.log('🔍 Auth error analysis:', errorDetails);

    // Only attempt recovery for recoverable errors
    if (errorDetails.canRecover && this.state.recoveryAttempts < this.maxRecoveryAttempts) {
      const now = Date.now();
      
      // Debounce recovery attempts
      if (now - this.state.lastErrorTime > this.recoveryDebounceMs) {
        await this.attemptRecovery(errorDetails);
      }
    }
  }

  /**
   * Analyze authentication error to determine type and recovery strategy
   */
  private analyzeAuthError(error: Error): AuthErrorDetails {
    const message = error.message.toLowerCase();
    
    if (message.includes('token') && message.includes('expired')) {
      return {
        type: 'token_expired',
        message: 'Authentication token has expired',
        canRecover: true,
        recoveryAction: 'refresh_token'
      };
    }
    
    if (message.includes('token') && (message.includes('invalid') || message.includes('malformed'))) {
      return {
        type: 'token_invalid',
        message: 'Authentication token is invalid',
        canRecover: true,
        recoveryAction: 'clear_auth'
      };
    }
    
    if (message.includes('network') || message.includes('fetch')) {
      return {
        type: 'network_error',
        message: 'Network error during authentication',
        canRecover: true,
        recoveryAction: 'retry_request'
      };
    }
    
    if (message.includes('auth') || message.includes('unauthorized')) {
      return {
        type: 'auth_state_error',
        message: 'Authentication state error',
        canRecover: true,
        recoveryAction: 'sync_state'
      };
    }
    
    return {
      type: 'unknown',
      message: error.message,
      canRecover: false,
      recoveryAction: 'manual_intervention'
    };
  }

  /**
   * Attempt automatic recovery based on error type
   */
  private async attemptRecovery(errorDetails: AuthErrorDetails): Promise<void> {
    this.setState({
      isRecovering: true,
      recoveryAttempts: this.state.recoveryAttempts + 1,
    });

    console.log(`🔄 Attempting recovery (${this.state.recoveryAttempts}/${this.maxRecoveryAttempts}):`, errorDetails.recoveryAction);

    try {
      switch (errorDetails.recoveryAction) {
        case 'refresh_token':
          await this.refreshTokenRecovery();
          break;
        case 'clear_auth':
          await this.clearAuthRecovery();
          break;
        case 'sync_state':
          await this.syncStateRecovery();
          break;
        case 'retry_request':
          await this.retryRequestRecovery();
          break;
        default:
          console.log('⚠️ No automatic recovery available for this error type');
      }

      // If recovery succeeded, reset error state
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        isRecovering: false,
      });

      console.log('✅ Auth error recovery successful');

    } catch (recoveryError) {
      console.error('❌ Auth error recovery failed:', recoveryError);
      
      this.setState({
        isRecovering: false,
      });
    }
  }

  /**
   * Recovery strategy: Refresh authentication token
   */
  private async refreshTokenRecovery(): Promise<void> {
    // This would typically involve calling the refresh token endpoint
    // For now, we'll sync the state and let the middleware handle it
    await stateSynchronizer.forceSynchronization();
  }

  /**
   * Recovery strategy: Clear authentication state
   */
  private async clearAuthRecovery(): Promise<void> {
    console.log('🧹 Clearing authentication state for recovery');
    
    // Clear Redux state
    store.dispatch(clearAuth());
    
    // Clear tokens
    clearAuthTokens();
    
    // Sync state
    await stateSynchronizer.forceSynchronization();
  }

  /**
   * Recovery strategy: Synchronize authentication state
   */
  private async syncStateRecovery(): Promise<void> {
    console.log('🔄 Synchronizing authentication state for recovery');
    
    const report = await stateSynchronizer.forceSynchronization();
    
    if (!report.isConsistent) {
      throw new Error(`State sync failed: ${report.issues.join(', ')}`);
    }
  }

  /**
   * Recovery strategy: Retry the failed request
   */
  private async retryRequestRecovery(): Promise<void> {
    console.log('🔄 Retrying request for recovery');
    
    // Wait a bit before retrying
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Force a state sync which might trigger the failed request again
    await stateSynchronizer.forceSynchronization();
  }

  /**
   * Manual recovery - clear error state and let user try again
   */
  private handleManualRecovery = (): void => {
    console.log('🔄 Manual recovery initiated');
    
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isRecovering: false,
      recoveryAttempts: 0,
    });
  };

  /**
   * Force clear all auth data
   */
  private handleForceClear = (): void => {
    console.log('🧹 Force clearing all authentication data');
    
    // Clear Redux state
    store.dispatch(clearAuth());
    
    // Clear tokens
    clearAuthTokens();
    
    // Reset error boundary state
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isRecovering: false,
      recoveryAttempts: 0,
    });
    
    // Reload page to ensure clean state
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const errorDetails = this.state.error ? this.analyzeAuthError(this.state.error) : null;

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">
                  Authentication Error
                </h3>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600">
                {errorDetails?.message || 'An authentication error occurred'}
              </p>
              
              {this.state.recoveryAttempts > 0 && (
                <p className="text-xs text-gray-500 mt-2">
                  Recovery attempts: {this.state.recoveryAttempts}/{this.maxRecoveryAttempts}
                </p>
              )}
            </div>

            {this.state.isRecovering ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-sm text-gray-600">Recovering...</span>
              </div>
            ) : (
              <div className="flex space-x-3">
                <button
                  onClick={this.handleManualRecovery}
                  className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Try Again
                </button>
                <button
                  onClick={this.handleForceClear}
                  className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Clear & Reload
                </button>
              </div>
            )}

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4">
                <summary className="text-xs text-gray-500 cursor-pointer">Debug Info</summary>
                <pre className="text-xs text-gray-600 mt-2 p-2 bg-gray-100 rounded overflow-auto">
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default AuthErrorBoundary;
