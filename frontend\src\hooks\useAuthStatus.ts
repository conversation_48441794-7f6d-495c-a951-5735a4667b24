/**
 * Optimized Auth Status Hook
 * Provides cached authentication status to reduce excessive token checking
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from './useAuth';
import { getAuthToken, getRefreshToken, isTokenValid, getTokenExpiry } from '../services/api';

interface AuthStatusInfo {
  hasTokens: boolean;
  tokensValid: boolean;
  userLoaded: boolean;
  isChecking: boolean;
  tokenExpiry: number | null;
  timeUntilExpiry: number | null;
  error?: string;
}

interface AuthStatusHookReturn extends AuthStatusInfo {
  refreshStatus: () => void;
  isExpiringSoon: boolean; // Token expires within 5 minutes
}

// Global cache to prevent multiple components from checking simultaneously
let globalAuthStatus: AuthStatusInfo | null = null;
let lastStatusCheck = 0;
const STATUS_CACHE_MS = 2000; // Cache status for 2 seconds

/**
 * Optimized hook for authentication status
 * Uses caching to prevent excessive token checking across components
 */
export const useAuthStatus = (): AuthStatusHookReturn => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [authStatus, setAuthStatus] = useState<AuthStatusInfo>(() => {
    // Initialize with cached status if available
    if (globalAuthStatus && (Date.now() - lastStatusCheck) < STATUS_CACHE_MS) {
      return globalAuthStatus;
    }
    
    return {
      hasTokens: false,
      tokensValid: false,
      userLoaded: false,
      isChecking: true,
      tokenExpiry: null,
      timeUntilExpiry: null,
    };
  });

  /**
   * Check authentication status with caching
   */
  const checkAuthStatus = useCallback(async () => {
    const now = Date.now();
    
    // Use cached status if recent
    if (globalAuthStatus && (now - lastStatusCheck) < STATUS_CACHE_MS) {
      setAuthStatus(globalAuthStatus);
      return;
    }

    setAuthStatus(prev => ({ ...prev, isChecking: true, error: undefined }));

    try {
      const token = getAuthToken();
      const refreshToken = getRefreshToken();
      const hasTokens = !!(token && refreshToken);

      if (!hasTokens) {
        const newStatus: AuthStatusInfo = {
          hasTokens: false,
          tokensValid: false,
          userLoaded: false,
          isChecking: false,
          tokenExpiry: null,
          timeUntilExpiry: null,
        };
        
        globalAuthStatus = newStatus;
        lastStatusCheck = now;
        setAuthStatus(newStatus);
        return;
      }

      // Use fast cached validation
      const tokensValid = isTokenValid();
      const tokenExpiry = getTokenExpiry();
      const timeUntilExpiry = tokenExpiry ? tokenExpiry - now : null;
      
      const newStatus: AuthStatusInfo = {
        hasTokens,
        tokensValid,
        userLoaded: !!user,
        isChecking: false,
        tokenExpiry,
        timeUntilExpiry,
      };

      globalAuthStatus = newStatus;
      lastStatusCheck = now;
      setAuthStatus(newStatus);

    } catch (error) {
      console.error('Auth status check failed:', error);
      const errorStatus: AuthStatusInfo = {
        hasTokens: false,
        tokensValid: false,
        userLoaded: false,
        isChecking: false,
        tokenExpiry: null,
        timeUntilExpiry: null,
        error: 'Authentication check failed',
      };
      
      globalAuthStatus = errorStatus;
      lastStatusCheck = now;
      setAuthStatus(errorStatus);
    }
  }, [user]);

  /**
   * Force refresh status (clears cache)
   */
  const refreshStatus = useCallback(() => {
    globalAuthStatus = null;
    lastStatusCheck = 0;
    checkAuthStatus();
  }, [checkAuthStatus]);

  /**
   * Check if token is expiring soon (within 5 minutes)
   */
  const isExpiringSoon = useMemo(() => {
    if (!authStatus.timeUntilExpiry || !authStatus.tokensValid) {
      return false;
    }
    return authStatus.timeUntilExpiry < 5 * 60 * 1000; // 5 minutes
  }, [authStatus.timeUntilExpiry, authStatus.tokensValid]);

  // Check status on mount and when dependencies change
  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  // Auto-refresh status every 30 seconds if tokens are valid
  useEffect(() => {
    if (!authStatus.tokensValid) return;

    const interval = setInterval(() => {
      // Only refresh if cache is stale
      if (Date.now() - lastStatusCheck > STATUS_CACHE_MS) {
        checkAuthStatus();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [authStatus.tokensValid, checkAuthStatus]);

  return {
    ...authStatus,
    refreshStatus,
    isExpiringSoon,
  };
};

/**
 * Clear global auth status cache
 * Useful when tokens are updated or cleared
 */
export const clearAuthStatusCache = (): void => {
  globalAuthStatus = null;
  lastStatusCheck = 0;
};

export default useAuthStatus;
