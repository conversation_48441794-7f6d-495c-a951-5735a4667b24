/**
 * Unified Quick Actions Section
 * Consolidated quick actions that adapt to different user roles
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useNavigate } from 'react-router-dom';
import { RTLText } from '../../../common';
import { DashboardRole, DashboardQuickAction } from '../../../../types/dashboard';
// Dashboard theme functionality moved to Redux - using default styling for now
import {
  Plus,
  Users,
  Calendar,
  BookOpen,
  MessageSquare,
  Settings,
  BarChart3,
  Shield,
  Eye,
  UserPlus,
  Briefcase,
  Target,
  Lightbulb,
  FileText,
  TrendingUp,
  DollarSign
} from 'lucide-react';

interface UnifiedQuickActionsSectionProps {
  role: DashboardRole;
  onUpdate?: (sectionId: string, data: any) => void;
  className?: string;
}

const UnifiedQuickActionsSection: React.FC<UnifiedQuickActionsSectionProps> = ({
  role,
  onUpdate,
  className = '',
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  // Default styling functions to replace removed dashboard theme
  const getCardClasses = () => 'glass-morphism border border-glass-border rounded-lg p-6';
  const getTextClasses = (type: 'primary' | 'secondary') =>
    type === 'primary' ? 'text-glass-primary' : 'text-glass-secondary';

  const [actions, setActions] = useState<DashboardQuickAction[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch role-specific quick actions
  useEffect(() => {
    const fetchQuickActions = async () => {
      try {
        setLoading(true);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 600));
        
        const roleActions = getRoleSpecificActions(role);
        setActions(roleActions);
        
        // Notify parent of data update
        onUpdate?.('quick_actions', roleActions);
      } catch (error) {
        console.error('Error fetching quick actions:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchQuickActions();
  }, [role, onUpdate]);

  // Get role-specific quick actions
  const getRoleSpecificActions = (userRole: DashboardRole): DashboardQuickAction[] => {
    switch (userRole) {
      case 'super_admin':
        return [
          {
            id: 'system_management',
            title: t('actions.systemManagement', 'System Management'),
            description: t('actions.systemManagement.desc', 'Manage system settings and configuration'),
            icon: Settings,
            href: '/super_admin/system-management',
            color: 'bg-red-600/20 hover:bg-red-600/30 border-red-500/30',
          },
          {
            id: 'user_management',
            title: t('actions.userManagement', 'User Management'),
            description: t('actions.userManagement.desc', 'Manage users and permissions'),
            icon: Users,
            href: '/super_admin/users',
            color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
          },
          {
            id: 'system_monitoring',
            title: t('actions.systemMonitoring', 'System Monitoring'),
            description: t('actions.systemMonitoring.desc', 'Monitor system health and performance'),
            icon: BarChart3,
            href: '/super_admin/monitoring',
            color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
          },
          {
            id: 'security_center',
            title: t('actions.securityCenter', 'Security Center'),
            description: t('actions.securityCenter.desc', 'Manage security settings and logs'),
            icon: Shield,
            href: '/super_admin/security',
            color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
          }
        ];

      case 'admin':
        return [
          {
            id: 'user_management',
            title: t('actions.userManagement', 'User Management'),
            description: t('actions.userManagement.desc', 'Manage users and roles'),
            icon: Users,
            href: '/admin/users',
            color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
          },
          {
            id: 'content_management',
            title: t('actions.contentManagement', 'Content Management'),
            description: t('actions.contentManagement.desc', 'Manage posts, events, and resources'),
            icon: BookOpen,
            href: '/admin/content',
            color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
          },
          {
            id: 'analytics',
            title: t('actions.analytics', 'Analytics'),
            description: t('actions.analytics.desc', 'View platform analytics and reports'),
            icon: BarChart3,
            href: '/admin/analytics',
            color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
          },
          {
            id: 'moderation',
            title: t('actions.moderation', 'Moderation'),
            description: t('actions.moderation.desc', 'Review and moderate content'),
            icon: Eye,
            href: '/admin/moderation',
            color: 'bg-orange-600/20 hover:bg-orange-600/30 border-orange-500/30',
          }
        ];

      case 'moderator':
        return [
          {
            id: 'review_reports',
            title: t('actions.reviewReports', 'Review Reports'),
            description: t('actions.reviewReports.desc', 'Review user reports and flagged content'),
            icon: Eye,
            href: '/dashboard/moderator/reports',
            color: 'bg-red-600/20 hover:bg-red-600/30 border-red-500/30',
            badge: 12,
          },
          {
            id: 'moderate_content',
            title: t('actions.moderateContent', 'Moderate Content'),
            description: t('actions.moderateContent.desc', 'Review and approve user content'),
            icon: Shield,
            href: '/dashboard/moderator/content',
            color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
            badge: 5,
          },
          {
            id: 'community_health',
            title: t('actions.communityHealth', 'Community Health'),
            description: t('actions.communityHealth.desc', 'Monitor community metrics'),
            icon: BarChart3,
            href: '/dashboard/moderator/health',
            color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
          }
        ];

      case 'mentor':
        return [
          {
            id: 'manage_mentees',
            title: t('actions.manageMentees', 'Manage Mentees'),
            description: t('actions.manageMentees.desc', 'View and manage your mentees'),
            icon: UserPlus,
            href: '/dashboard/mentor/mentees',
            color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
          },
          {
            id: 'schedule_session',
            title: t('actions.scheduleSession', 'Schedule Session'),
            description: t('actions.scheduleSession.desc', 'Schedule a mentoring session'),
            icon: Calendar,
            href: '/dashboard/mentor/schedule',
            color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
          },
          {
            id: 'resources',
            title: t('actions.resources', 'Resources'),
            description: t('actions.resources.desc', 'Access mentoring resources'),
            icon: BookOpen,
            href: '/dashboard/mentor/resources',
            color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
          }
        ];

      case 'investor':
        return [
          {
            id: 'view_opportunities',
            title: t('actions.viewOpportunities', 'View Opportunities'),
            description: t('actions.viewOpportunities.desc', 'Browse investment opportunities'),
            icon: Target,
            href: '/dashboard/investor/opportunities',
            color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
            badge: 15,
          },
          {
            id: 'portfolio',
            title: t('actions.portfolio', 'Portfolio'),
            description: t('actions.portfolio.desc', 'Manage your investment portfolio'),
            icon: Briefcase,
            href: '/dashboard/investor/portfolio',
            color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
          },
          {
            id: 'analytics',
            title: t('actions.analytics', 'Analytics'),
            description: t('actions.analytics.desc', 'View investment analytics'),
            icon: TrendingUp,
            href: '/dashboard/investor/analytics',
            color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
          }
        ];

      default: // user
        return [
          {
            id: 'create_idea',
            title: t('actions.createIdea', 'Create Business Idea'),
            description: t('actions.createIdea.desc', 'Submit a new business idea'),
            icon: Lightbulb,
            href: '/dashboard/business-ideas/create',
            color: 'bg-indigo-600/20 hover:bg-indigo-600/30 border-indigo-500/30',
          },
          {
            id: 'view_ideas',
            title: t('actions.viewIdeas', 'My Ideas'),
            description: t('actions.viewIdeas.desc', 'View and manage your ideas'),
            icon: FileText,
            href: '/dashboard/business-ideas',
            color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
          },
          {
            id: 'resources',
            title: t('actions.resources', 'Resources'),
            description: t('actions.resources.desc', 'Access learning resources'),
            icon: BookOpen,
            href: '/dashboard/resources',
            color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
          },
          {
            id: 'mentorship',
            title: t('actions.mentorship', 'Find Mentor'),
            description: t('actions.mentorship.desc', 'Connect with mentors'),
            icon: Users,
            href: '/dashboard/mentorship',
            color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
          }
        ];
    }
  };

  // Handle action click
  const handleActionClick = (action: DashboardQuickAction) => {
    if (action.onClick) {
      action.onClick();
    } else if (action.href) {
      navigate(action.href);
    }
  };

  // Render action card
  const renderActionCard = (action: DashboardQuickAction) => {
    const IconComponent = action.icon;
    
    return (
      <button
        key={action.id}
        onClick={() => handleActionClick(action)}
        disabled={action.disabled || loading}
        className={`
          ${action.color} border rounded-lg p-4 text-left transition-all duration-200
          hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
          disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100
          ${isRTL ? 'text-right' : 'text-left'}
        `}
      >
        <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <IconComponent className={`w-5 h-5 text-white ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <RTLText as="h3" className={`font-semibold ${getTextClasses('primary')}`}>
                {action.title}
              </RTLText>
              {action.badge && (
                <span className={`
                  ${isRTL ? 'mr-2' : 'ml-2'} px-2 py-1 text-xs font-medium 
                  bg-red-500 text-white rounded-full
                `}>
                  {action.badge}
                </span>
              )}
            </div>
            <RTLText as="p" className={`text-sm ${getTextClasses('secondary')} mt-1`}>
              {action.description}
            </RTLText>
          </div>
        </div>
      </button>
    );
  };

  return (
    <div className={className}>
      <div className="mb-6">
        <RTLText as="h2" className={`text-xl font-semibold ${getTextClasses('primary')}`}>
          {t('dashboard.quickActions', 'Quick Actions')}
        </RTLText>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="bg-white/10 border border-white/20 rounded-lg p-4 animate-pulse">
              <div className="flex items-center mb-2">
                <div className="w-5 h-5 bg-white/20 rounded mr-2"></div>
                <div className="h-4 bg-white/20 rounded w-24"></div>
              </div>
              <div className="h-3 bg-white/20 rounded w-full"></div>
            </div>
          ))
        ) : (
          actions.map(renderActionCard)
        )}
      </div>
    </div>
  );
};

export default UnifiedQuickActionsSection;
