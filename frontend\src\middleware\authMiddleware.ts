/**
 * Authentication Middleware
 * Handles automatic token validation, refresh, and Redux state synchronization
 */

import { Middleware } from '@reduxjs/toolkit';
import { getAuthToken, getRefreshToken, clearAuthTokens, authAPI } from '../services/api';
import { clearAuth, setAuthState, getCurrentUser } from '../store/authSlice';

// Enhanced state management for token refresh and auth checking
interface AuthMiddlewareState {
  isRefreshing: boolean;
  refreshPromise: Promise<boolean> | null;
  lastTokenCheck: number;
  tokenCheckCount: number;
  lastAuthCheck: number;
  authCheckInProgress: boolean;
}

const authState: AuthMiddlewareState = {
  isRefreshing: false,
  refreshPromise: null,
  lastTokenCheck: 0,
  tokenCheckCount: 0,
  lastAuthCheck: 0,
  authCheckInProgress: false,
};

// Debouncing constants
const TOKEN_CHECK_DEBOUNCE_MS = 100; // Minimum time between token checks
const AUTH_CHECK_DEBOUNCE_MS = 500; // Minimum time between auth status checks
const MAX_TOKEN_CHECKS_PER_SECOND = 10; // Maximum token checks per second

/**
 * Authentication middleware that:
 * 1. Monitors authentication state
 * 2. Automatically refreshes expired tokens
 * 3. Syncs Redux state with token status
 * 4. Handles authentication errors gracefully
 */
export const authMiddleware: Middleware = (store) => (next) => (action) => {
  const result = next(action);

  // Check authentication status on relevant actions
  if (shouldCheckAuth(action.type)) {
    checkAuthenticationStatus(store);
  }

  return result;
};

/**
 * Determines if we should check authentication status for this action
 * Now includes debouncing logic to prevent excessive checks
 */
function shouldCheckAuth(actionType: string): boolean {
  const now = Date.now();

  // Reset token check counter every second
  if (now - authState.lastTokenCheck > 1000) {
    authState.tokenCheckCount = 0;
    authState.lastTokenCheck = now;
  }

  // Prevent excessive token checking
  if (authState.tokenCheckCount >= MAX_TOKEN_CHECKS_PER_SECOND) {
    console.warn('⚠️ Auth middleware: Token check rate limit reached, skipping check');
    return false;
  }

  // Debounce auth checks
  if (now - authState.lastAuthCheck < AUTH_CHECK_DEBOUNCE_MS) {
    return false;
  }

  // Skip if auth check is already in progress
  if (authState.authCheckInProgress) {
    return false;
  }

  const authCheckActions = [
    'auth/login/fulfilled',
    'auth/logout/fulfilled',
    'auth/getCurrentUser/rejected',
    'auth/initializeAuth/rejected',
    '@@INIT', // Redux initialization
  ];

  const shouldCheck = authCheckActions.some(type => actionType.includes(type)) ||
                     actionType.includes('rejected'); // Check on any API failure

  if (shouldCheck) {
    authState.tokenCheckCount++;
    authState.lastAuthCheck = now;
  }

  return shouldCheck;
}

/**
 * Checks current authentication status and syncs with Redux
 */
async function checkAuthenticationStatus(store: any) {
  const { dispatch, getState } = store;
  const currentState = getState();
  const authState = currentState.auth;

  const token = getAuthToken();
  const refreshToken = getRefreshToken();

  // If no tokens exist, clear auth state
  if (!token && !refreshToken) {
    if (authState.isAuthenticated) {
      console.log('🔐 No tokens found, clearing auth state');
      dispatch(clearAuth());
    }
    return;
  }

  // If we have tokens but no user in Redux, try to get current user
  if (token && !authState.user && !authState.isLoading) {
    console.log('🔐 Token exists but no user in Redux, fetching current user');
    dispatch(getCurrentUser());
    return;
  }

  // Check if token is expired and needs refresh
  if (token && isTokenExpired(token)) {
    console.log('🔐 Token expired, attempting refresh');
    await handleTokenRefresh(dispatch);
  }
}

/**
 * Checks if a JWT token is expired
 */
function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const now = Math.floor(Date.now() / 1000);
    return payload.exp < now;
  } catch (error) {
    console.error('Error parsing token:', error);
    return true; // Treat invalid tokens as expired
  }
}

/**
 * Handles token refresh with proper error handling and state management
 */
async function handleTokenRefresh(dispatch: any): Promise<boolean> {
  // Prevent multiple simultaneous refresh attempts
  if (isRefreshing && refreshPromise) {
    return refreshPromise;
  }

  isRefreshing = true;
  refreshPromise = performTokenRefresh(dispatch);

  try {
    const success = await refreshPromise;
    return success;
  } finally {
    isRefreshing = false;
    refreshPromise = null;
  }
}

/**
 * Performs the actual token refresh
 */
async function performTokenRefresh(dispatch: any): Promise<boolean> {
  try {
    const success = await authAPI.refreshToken();
    
    if (success) {
      console.log('✅ Token refreshed successfully');
      // Fetch updated user data
      dispatch(getCurrentUser());
      return true;
    } else {
      console.log('❌ Token refresh failed, clearing auth state');
      clearAuthTokens();
      dispatch(clearAuth());
      return false;
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    clearAuthTokens();
    dispatch(clearAuth());
    return false;
  }
}

/**
 * Enhanced API request wrapper that automatically handles authentication
 */
export function createAuthenticatedApiRequest(store: any) {
  return async function authenticatedApiRequest<T>(
    endpoint: string,
    method: string = 'GET',
    data?: any
  ): Promise<T> {
    const { dispatch } = store;
    
    try {
      // Import the apiRequest function dynamically to avoid circular imports
      const { apiRequest } = await import('../services/api');
      return await apiRequest<T>(endpoint, method, data);
    } catch (error: any) {
      // Handle authentication errors
      if (error?.status === 401) {
        console.log('🔐 API request failed with 401, attempting token refresh');
        const refreshSuccess = await handleTokenRefresh(dispatch);
        
        if (refreshSuccess) {
          // Retry the request after successful refresh
          const { apiRequest } = await import('../services/api');
          return await apiRequest<T>(endpoint, method, data);
        } else {
          // Redirect to login if refresh fails
          window.location.href = '/login';
          throw new Error('Authentication failed. Please log in again.');
        }
      }
      
      throw error;
    }
  };
}

/**
 * Hook to get authenticated API request function
 */
export function useAuthenticatedApi() {
  // This would be used in components to get the authenticated API function
  // Implementation would depend on how you want to integrate it with your components
}

export default authMiddleware;
