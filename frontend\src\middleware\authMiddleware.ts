/**
 * Authentication Middleware
 * Handles automatic token validation, refresh, and Redux state synchronization
 */

import { Middleware } from '@reduxjs/toolkit';
import { getAuthToken, getRefreshToken, clearAuthTokens, authAPI } from '../services/api';
import { clearAuth, setAuthState, getCurrentUser } from '../store/authSlice';
import { authLogger } from '../utils/authLogger';
import { performanceMonitor } from '../utils/performanceMonitor';

// Enhanced state management for token refresh and auth checking
interface AuthMiddlewareState {
  isRefreshing: boolean;
  refreshPromise: Promise<boolean> | null;
  lastTokenCheck: number;
  tokenCheckCount: number;
  lastAuthCheck: number;
  authCheckInProgress: boolean;
}

const authState: AuthMiddlewareState = {
  isRefreshing: false,
  refreshPromise: null,
  lastTokenCheck: 0,
  tokenCheckCount: 0,
  lastAuthCheck: 0,
  authCheckInProgress: false,
};

// Debouncing constants
const TOKEN_CHECK_DEBOUNCE_MS = 100; // Minimum time between token checks
const AUTH_CHECK_DEBOUNCE_MS = 500; // Minimum time between auth status checks
const MAX_TOKEN_CHECKS_PER_SECOND = 10; // Maximum token checks per second

// Performance monitoring
interface AuthPerformanceMetrics {
  totalTokenChecks: number;
  totalAuthChecks: number;
  totalRefreshAttempts: number;
  successfulRefreshes: number;
  failedRefreshes: number;
  averageRefreshTime: number;
  lastResetTime: number;
}

const performanceMetrics: AuthPerformanceMetrics = {
  totalTokenChecks: 0,
  totalAuthChecks: 0,
  totalRefreshAttempts: 0,
  successfulRefreshes: 0,
  failedRefreshes: 0,
  averageRefreshTime: 0,
  lastResetTime: Date.now(),
};

// Reset metrics every hour
setInterval(() => {
  const now = Date.now();
  const hoursSinceReset = (now - performanceMetrics.lastResetTime) / (1000 * 60 * 60);

  if (hoursSinceReset >= 1) {
    console.log('📊 Auth Middleware Performance (last hour):', {
      tokenChecks: performanceMetrics.totalTokenChecks,
      authChecks: performanceMetrics.totalAuthChecks,
      refreshAttempts: performanceMetrics.totalRefreshAttempts,
      successfulRefreshes: performanceMetrics.successfulRefreshes,
      failedRefreshes: performanceMetrics.failedRefreshes,
      averageRefreshTime: `${performanceMetrics.averageRefreshTime}ms`,
      refreshSuccessRate: performanceMetrics.totalRefreshAttempts > 0
        ? `${((performanceMetrics.successfulRefreshes / performanceMetrics.totalRefreshAttempts) * 100).toFixed(1)}%`
        : 'N/A'
    });

    // Reset metrics
    Object.assign(performanceMetrics, {
      totalTokenChecks: 0,
      totalAuthChecks: 0,
      totalRefreshAttempts: 0,
      successfulRefreshes: 0,
      failedRefreshes: 0,
      averageRefreshTime: 0,
      lastResetTime: now,
    });
  }
}, 60000); // Check every minute

/**
 * Authentication middleware that:
 * 1. Monitors authentication state
 * 2. Automatically refreshes expired tokens
 * 3. Syncs Redux state with token status
 * 4. Handles authentication errors gracefully
 */
export const authMiddleware: Middleware = (store) => (next) => (action) => {
  // Performance tracking with enhanced logging
  const measurementId = performanceMonitor.startMeasurement(
    `middleware_${action.type}`,
    'middleware',
    { actionType: action.type, hasPayload: !!action.payload }
  );

  try {
    const result = next(action);

    // Log auth-related actions
    if (action.type.startsWith('auth/')) {
      const currentState = store.getState().auth;
      authLogger.logAuthStateChange(
        action.type,
        currentState,
        currentState,
        currentState.user?.id
      );
    }

    // Check authentication status on relevant actions
    if (shouldCheckAuth(action.type)) {
      checkAuthenticationStatus(store);
    }

    // Complete performance measurement
    performanceMonitor.endMeasurement(measurementId, {
      success: true,
      actionType: action.type
    });

    return result;
  } catch (error) {
    // Complete performance measurement with error
    performanceMonitor.endMeasurement(measurementId, {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });

    // Log error
    authLogger.logError('middleware', action.type, error instanceof Error ? error : new Error(String(error)), {
      actionType: action.type,
      payload: action.payload
    });

    throw error;
  }
};

/**
 * Determines if we should check authentication status for this action
 * Now includes debouncing logic to prevent excessive checks
 */
function shouldCheckAuth(actionType: string): boolean {
  const now = Date.now();

  // Reset token check counter every second
  if (now - authState.lastTokenCheck > 1000) {
    authState.tokenCheckCount = 0;
    authState.lastTokenCheck = now;
  }

  // Prevent excessive token checking
  if (authState.tokenCheckCount >= MAX_TOKEN_CHECKS_PER_SECOND) {
    console.warn('⚠️ Auth middleware: Token check rate limit reached, skipping check');
    return false;
  }

  // Debounce auth checks
  if (now - authState.lastAuthCheck < AUTH_CHECK_DEBOUNCE_MS) {
    return false;
  }

  // Skip if auth check is already in progress
  if (authState.authCheckInProgress) {
    return false;
  }

  const authCheckActions = [
    'auth/login/fulfilled',
    'auth/logout/fulfilled',
    'auth/getCurrentUser/rejected',
    'auth/initializeAuth/rejected',
    '@@INIT', // Redux initialization
  ];

  const shouldCheck = authCheckActions.some(type => actionType.includes(type)) ||
                     actionType.includes('rejected'); // Check on any API failure

  if (shouldCheck) {
    authState.tokenCheckCount++;
    authState.lastAuthCheck = now;
    performanceMetrics.totalTokenChecks++;
    performanceMetrics.totalAuthChecks++;
  }

  return shouldCheck;
}

/**
 * Checks current authentication status and syncs with Redux
 * Enhanced with race condition prevention and better error handling
 */
async function checkAuthenticationStatus(store: any) {
  // Prevent concurrent auth checks
  if (authState.authCheckInProgress) {
    console.log('🔄 Auth check already in progress, skipping');
    return;
  }

  authState.authCheckInProgress = true;

  try {
    const { dispatch, getState } = store;
    const currentState = getState();
    const reduxAuthState = currentState.auth;

    const token = getAuthToken();
    const refreshToken = getRefreshToken();

    // Log token check
    authLogger.logTokenOperation('token_check', {
      hasToken: !!token,
      hasRefreshToken: !!refreshToken,
      reduxAuthenticated: reduxAuthState.isAuthenticated
    }, reduxAuthState.user?.id);

    // If no tokens exist, clear auth state
    if (!token && !refreshToken) {
      if (reduxAuthState.isAuthenticated) {
        authLogger.log('info', 'auth', 'clearing_auth_no_tokens', {
          reason: 'No tokens found but Redux shows authenticated'
        }, reduxAuthState.user?.id);
        dispatch(clearAuth());
      }
      return;
    }

    // If we have tokens but no user in Redux, try to get current user
    if (token && !reduxAuthState.user && !reduxAuthState.isLoading) {
      console.log('🔐 Token exists but no user in Redux, fetching current user');
      dispatch(getCurrentUser());
      return;
    }
  } catch (error) {
    console.error('❌ Error in auth status check:', error);
  } finally {
    authState.authCheckInProgress = false;
  }

  // Check if token is expired and needs refresh
  if (token && isTokenExpired(token)) {
    console.log('🔐 Token expired, attempting refresh');
    await handleTokenRefresh(dispatch);
  }
}

/**
 * Checks if a JWT token is expired
 */
function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const now = Math.floor(Date.now() / 1000);
    return payload.exp < now;
  } catch (error) {
    console.error('Error parsing token:', error);
    return true; // Treat invalid tokens as expired
  }
}

/**
 * Handles token refresh with enhanced race condition prevention
 */
async function handleTokenRefresh(dispatch: any): Promise<boolean> {
  // If already refreshing, wait for the existing promise
  if (authState.isRefreshing && authState.refreshPromise) {
    console.log('🔄 Token refresh already in progress, waiting...');
    return await authState.refreshPromise;
  }

  // Prevent multiple refresh attempts
  if (authState.isRefreshing) {
    console.log('⚠️ Token refresh in progress but no promise found, skipping');
    return false;
  }

  // Start refresh process
  console.log('🔄 Starting token refresh...');
  const refreshStartTime = Date.now();
  authState.isRefreshing = true;
  authState.refreshPromise = performTokenRefresh(dispatch);
  performanceMetrics.totalRefreshAttempts++;

  try {
    const result = await authState.refreshPromise;
    const refreshDuration = Date.now() - refreshStartTime;

    // Update performance metrics
    if (result) {
      performanceMetrics.successfulRefreshes++;
    } else {
      performanceMetrics.failedRefreshes++;
    }

    // Update average refresh time
    const totalRefreshes = performanceMetrics.successfulRefreshes + performanceMetrics.failedRefreshes;
    performanceMetrics.averageRefreshTime =
      (performanceMetrics.averageRefreshTime * (totalRefreshes - 1) + refreshDuration) / totalRefreshes;

    console.log(`✅ Token refresh ${result ? 'successful' : 'failed'} (${refreshDuration}ms)`);
    return result;
  } catch (error) {
    const refreshDuration = Date.now() - refreshStartTime;
    performanceMetrics.failedRefreshes++;
    console.error(`❌ Token refresh error (${refreshDuration}ms):`, error);
    return false;
  } finally {
    authState.isRefreshing = false;
    authState.refreshPromise = null;
  }
}

/**
 * Performs the actual token refresh
 */
async function performTokenRefresh(dispatch: any): Promise<boolean> {
  try {
    const success = await authAPI.refreshToken();
    
    if (success) {
      console.log('✅ Token refreshed successfully');
      // Fetch updated user data
      dispatch(getCurrentUser());
      return true;
    } else {
      console.log('❌ Token refresh failed, clearing auth state');
      clearAuthTokens();
      dispatch(clearAuth());
      return false;
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    clearAuthTokens();
    dispatch(clearAuth());
    return false;
  }
}

/**
 * Enhanced API request wrapper that automatically handles authentication
 */
export function createAuthenticatedApiRequest(store: any) {
  return async function authenticatedApiRequest<T>(
    endpoint: string,
    method: string = 'GET',
    data?: any
  ): Promise<T> {
    const { dispatch } = store;
    
    try {
      // Import the apiRequest function dynamically to avoid circular imports
      const { apiRequest } = await import('../services/api');
      return await apiRequest<T>(endpoint, method, data);
    } catch (error: any) {
      // Handle authentication errors
      if (error?.status === 401) {
        console.log('🔐 API request failed with 401, attempting token refresh');
        const refreshSuccess = await handleTokenRefresh(dispatch);
        
        if (refreshSuccess) {
          // Retry the request after successful refresh
          const { apiRequest } = await import('../services/api');
          return await apiRequest<T>(endpoint, method, data);
        } else {
          // Redirect to login if refresh fails
          window.location.href = '/login';
          throw new Error('Authentication failed. Please log in again.');
        }
      }
      
      throw error;
    }
  };
}

/**
 * Hook to get authenticated API request function
 */
export function useAuthenticatedApi() {
  // This would be used in components to get the authenticated API function
  // Implementation would depend on how you want to integrate it with your components
}

/**
 * Get current auth middleware performance metrics
 */
export function getAuthPerformanceMetrics(): AuthPerformanceMetrics & {
  uptime: string;
  tokenChecksPerMinute: number;
  authChecksPerMinute: number;
} {
  const now = Date.now();
  const uptimeMs = now - performanceMetrics.lastResetTime;
  const uptimeMinutes = uptimeMs / (1000 * 60);

  return {
    ...performanceMetrics,
    uptime: `${Math.floor(uptimeMinutes)} minutes`,
    tokenChecksPerMinute: uptimeMinutes > 0 ? Math.round(performanceMetrics.totalTokenChecks / uptimeMinutes) : 0,
    authChecksPerMinute: uptimeMinutes > 0 ? Math.round(performanceMetrics.totalAuthChecks / uptimeMinutes) : 0,
  };
}

// Export for debugging
if (typeof window !== 'undefined') {
  (window as any).authMiddlewareMetrics = getAuthPerformanceMetrics;
  console.log('📊 Auth middleware metrics available: authMiddlewareMetrics()');
}

export default authMiddleware;
